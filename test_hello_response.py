#!/usr/bin/env python3
"""
专门测试"你好"回复提取
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_hello_response():
    """测试你好回复"""
    print("👋 专门测试'你好'回复提取...")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=180000  # 180秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送"你好"
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程以便调试
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复完成...")
            
            # 等待回答完成
            max_wait_time = 120
            completion_detected = False
            
            for wait_time in range(0, max_wait_time, 3):
                completion_status = await bot._check_completion_status()
                if completion_status:
                    print(f"✅ 在第{wait_time}秒检测到'回答完成'状态！")
                    completion_detected = True
                    break
                print(f"⏳ 等待回答完成... {wait_time}秒")
                await asyncio.sleep(3)
            
            if not completion_detected:
                print("❌ 未检测到'回答完成'状态，但继续尝试提取...")
            
            # 等待一下确保内容完全加载
            await asyncio.sleep(3)
            
            print("\n🔍 开始查找'你好呀'回复...")
            
            # 查找所有短文本
            short_texts = await bot.page.evaluate("""
                () => {
                    const allElements = document.querySelectorAll('*');
                    const shortTexts = [];
                    
                    for (let element of allElements) {
                        const text = element.innerText || '';
                        const trimmed = text.trim();
                        
                        if (trimmed.length >= 2 && trimmed.length <= 20) {
                            shortTexts.push({
                                text: trimmed,
                                tagName: element.tagName,
                                className: element.className || '',
                                id: element.id || ''
                            });
                        }
                    }
                    
                    return shortTexts;
                }
            """)
            
            print(f"\n📊 找到 {len(short_texts)} 个短文本:")
            hello_candidates = []
            
            for i, item in enumerate(short_texts, 1):
                text = item['text']
                print(f"[{i}] '{text}' (标签: {item['tagName']}, 类: {item['className'][:30]})")
                
                # 检查是否包含"你好"相关词汇
                if ('你好' in text or '呀' in text or 'hi' in text.lower() or 
                    'hello' in text.lower() or '👋' in text):
                    print(f"    🎯 可能是问候回复!")
                    hello_candidates.append(text)
            
            print(f"\n🎯 找到 {len(hello_candidates)} 个可能的问候回复:")
            for candidate in hello_candidates:
                print(f"  - '{candidate}'")
            
            # 尝试使用新的提取方法
            print(f"\n🧪 尝试使用新的提取方法...")
            try:
                response = await bot._extract_ai_response()
                print(f"✅ 成功提取AI回复: '{response}'")
                
                if response == "你好呀" or "你好" in response:
                    print("🎉 完美！成功提取到预期的'你好呀'回复！")
                else:
                    print(f"⚠️  提取到回复，但不是预期的'你好呀': '{response}'")
                    
            except Exception as e:
                print(f"❌ 提取失败: {e}")
                
                # 如果提取失败，显示页面上所有可能的文本
                print("\n🔍 显示页面上所有可能的文本内容:")
                all_texts = await bot.page.evaluate("""
                    () => {
                        const allText = document.body.innerText || '';
                        return allText.split('\\n').filter(line => line.trim().length > 0);
                    }
                """)
                
                for i, line in enumerate(all_texts[:20], 1):  # 只显示前20行
                    print(f"  {i}: {line.strip()}")
            
            # 保持浏览器打开以便手动检查
            print(f"\n⏸️  浏览器将保持打开20秒，请手动检查是否有'你好呀'回复...")
            await asyncio.sleep(20)
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_hello_response())
