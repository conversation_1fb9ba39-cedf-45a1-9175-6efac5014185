#!/usr/bin/env python3
"""
测试通用AI回复提取 - 不依赖特定关键词
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_universal_extraction():
    """测试通用提取逻辑"""
    print("🌐 测试通用AI回复提取...")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=180000  # 180秒超时
    )
    
    # 测试不同类型的问题
    test_messages = [
        "你好",
        "深圳有哪些好玩的地方？",
        "请解释一下什么是人工智能",
        "今天天气怎么样？"
    ]
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            for i, message in enumerate(test_messages, 1):
                print(f"\n📤 测试 {i}: {message}")
                print("-" * 40)
                
                try:
                    # 手动执行发送过程以便调试
                    await bot._input_message_stealth(message)
                    await bot._click_send_button_stealth()
                    
                    print("✅ 消息已发送，等待回复...")
                    
                    # 等待回答完成
                    max_wait_time = 120
                    completion_detected = False
                    
                    for wait_time in range(0, max_wait_time, 5):
                        completion_status = await bot._check_completion_status()
                        if completion_status:
                            print(f"✅ 在第{wait_time}秒检测到'回答完成'状态！")
                            completion_detected = True
                            break
                        print(f"⏳ 等待回答完成... {wait_time}秒")
                        await asyncio.sleep(5)
                    
                    if not completion_detected:
                        print("❌ 未检测到'回答完成'状态，跳过此测试")
                        continue
                    
                    # 等待内容完全加载
                    await asyncio.sleep(3)
                    
                    # 使用通用方法提取回复
                    print("🔍 使用通用方法提取回复...")
                    
                    response = await bot.page.evaluate("""
                        () => {
                            // 查找"回答完成"后的内容
                            const allElements = document.querySelectorAll('*');
                            let completionElement = null;
                            
                            for (let element of allElements) {
                                const text = element.innerText || '';
                                if (text.includes('回答完成')) {
                                    completionElement = element;
                                    break;
                                }
                            }
                            
                            if (completionElement) {
                                let current = completionElement;
                                
                                while (current && current.parentElement) {
                                    const parent = current.parentElement;
                                    const siblings = Array.from(parent.children);
                                    const currentIndex = siblings.indexOf(current);
                                    
                                    for (let i = currentIndex + 1; i < siblings.length; i++) {
                                        const sibling = siblings[i];
                                        const siblingText = sibling.innerText || '';
                                        
                                        // 通用条件：长度合适且不是界面元素
                                        if (siblingText.length > 10 && siblingText.length < 5000 &&
                                            !siblingText.includes('DeepSeek-R1满血版') &&
                                            !siblingText.includes('回答完成') &&
                                            !siblingText.includes('思考完成') &&
                                            !siblingText.includes('思考中') &&
                                            !siblingText.includes('登录') &&
                                            !siblingText.includes('注册') &&
                                            !siblingText.includes('百度首页') &&
                                            !siblingText.includes('发送') &&
                                            !siblingText.includes('提问') &&
                                            !siblingText.includes('搜索')) {
                                            
                                            console.log('找到回答完成后的内容:', siblingText.substring(0, 100));
                                            return siblingText.trim();
                                        }
                                    }
                                    
                                    current = parent;
                                }
                            }
                            
                            return null;
                        }
                    """)
                    
                    if response:
                        print(f"📥 成功提取回复:")
                        print(f"长度: {len(response)} 字符")
                        print(f"内容: {response[:200]}...")
                        print("✅ 提取成功！")
                    else:
                        print("❌ 未能提取到回复内容")
                    
                    # 等待一下再发送下一个问题
                    if i < len(test_messages):
                        print("⏳ 等待3秒后发送下一个问题...")
                        await asyncio.sleep(3)
                        
                except Exception as e:
                    print(f"❌ 测试 {i} 失败: {e}")
                    continue
            
            print(f"\n✅ 通用提取测试完成！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_universal_extraction())
