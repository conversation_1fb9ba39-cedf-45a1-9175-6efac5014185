#!/usr/bin/env python3
"""
测试图标点击的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_icon_click():
    """测试图标点击方案"""
    print("🎯 测试图标点击方案")
    print("=" * 50)
    
    config = BaiduChatConfig(
        base_url="https://chat.baidu.com/",
        response_timeout=180000,
        keep_browser_open=True,
        debug=True
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复...")
            await bot._wait_for_completion_status()
            print("✅ 回答完成，开始测试图标点击...")
            
            # 查找复制图标
            copy_icons = await bot.page.query_selector_all('.cos-icon-copy')
            print(f"找到 {len(copy_icons)} 个复制图标")
            
            if copy_icons:
                # 选择最后一个图标（最新的回复）
                target_icon = copy_icons[-1]
                
                print("🖱️  尝试点击复制图标...")
                
                try:
                    # 检查图标是否可见
                    is_visible = await target_icon.is_visible()
                    print(f"图标可见性: {is_visible}")
                    
                    if not is_visible:
                        print("图标不可见，尝试悬停激活...")
                        # 查找图标的父元素并悬停
                        parent = await target_icon.evaluate_handle("el => el.parentElement")
                        if parent:
                            await parent.hover()
                            await asyncio.sleep(2)
                            is_visible = await target_icon.is_visible()
                            print(f"悬停后图标可见性: {is_visible}")
                    
                    # 尝试点击
                    if is_visible:
                        await target_icon.scroll_into_view_if_needed()
                        await asyncio.sleep(1)
                        await target_icon.click()
                        print("✅ 图标点击成功")
                    else:
                        # 如果图标不可见，尝试点击父元素
                        print("图标不可见，尝试点击父元素...")
                        parent = await target_icon.evaluate_handle("el => el.parentElement")
                        if parent:
                            await parent.click()
                            print("✅ 父元素点击成功")
                    
                    # 等待复制完成
                    await asyncio.sleep(2)
                    
                    # 测试剪贴板读取
                    print("📋 测试剪贴板读取...")
                    
                    # 方法1: 直接读取
                    clipboard_content = await bot.page.evaluate("""
                        async () => {
                            try {
                                return await navigator.clipboard.readText();
                            } catch (e) {
                                return 'Error: ' + e.message;
                            }
                        }
                    """)
                    
                    if clipboard_content and not clipboard_content.startswith('Error:'):
                        print(f"✅ 剪贴板内容: {clipboard_content[:200]}...")
                        
                        # 验证内容是否是AI回复
                        if len(clipboard_content) > 10:
                            print("🎉 成功通过复制图标获取AI回复！")
                            return clipboard_content
                    else:
                        print(f"❌ 剪贴板读取失败: {clipboard_content}")
                        
                        # 尝试其他方法
                        print("尝试其他剪贴板读取方法...")
                        
                        # 方法2: 临时输入框
                        clipboard_content2 = await bot.page.evaluate("""
                            () => {
                                const tempInput = document.createElement('textarea');
                                tempInput.style.position = 'fixed';
                                tempInput.style.left = '-9999px';
                                document.body.appendChild(tempInput);
                                tempInput.focus();
                                document.execCommand('paste');
                                const content = tempInput.value;
                                document.body.removeChild(tempInput);
                                return content;
                            }
                        """)
                        
                        if clipboard_content2:
                            print(f"✅ 方法2成功: {clipboard_content2[:200]}...")
                            return clipboard_content2
                        
                        # 方法3: Ctrl+V
                        print("尝试Ctrl+V方法...")
                        await bot.page.evaluate("""
                            () => {
                                const tempDiv = document.createElement('div');
                                tempDiv.contentEditable = true;
                                tempDiv.id = 'temp-paste-area';
                                tempDiv.style.position = 'fixed';
                                tempDiv.style.left = '-9999px';
                                document.body.appendChild(tempDiv);
                                tempDiv.focus();
                            }
                        """)
                        
                        await bot.page.keyboard.press('Control+v')
                        await asyncio.sleep(1)
                        
                        clipboard_content3 = await bot.page.evaluate("""
                            () => {
                                const tempDiv = document.getElementById('temp-paste-area');
                                const content = tempDiv ? tempDiv.innerText : null;
                                if (tempDiv) document.body.removeChild(tempDiv);
                                return content;
                            }
                        """)
                        
                        if clipboard_content3:
                            print(f"✅ 方法3成功: {clipboard_content3[:200]}...")
                            return clipboard_content3
                        
                        print("❌ 所有剪贴板读取方法都失败")
                    
                except Exception as e:
                    print(f"❌ 点击图标失败: {e}")
            else:
                print("❌ 未找到复制图标")
            
            # 尝试其他复制相关元素
            print("\n🔍 尝试其他复制相关元素...")
            other_selectors = [
                'span:has(.cos-icon-copy)',
                '.menu-item_19lnq_32',
                '[data-click-log*="copy"]'
            ]
            
            for selector in other_selectors:
                try:
                    elements = await bot.page.query_selector_all(selector)
                    if elements:
                        print(f"找到 {len(elements)} 个 {selector} 元素")
                        target = elements[-1]
                        
                        is_visible = await target.is_visible()
                        print(f"{selector} 可见性: {is_visible}")
                        
                        if is_visible:
                            await target.click()
                            print(f"✅ 点击 {selector} 成功")
                            await asyncio.sleep(2)
                            
                            # 测试剪贴板
                            clipboard_test = await bot.page.evaluate("""
                                async () => {
                                    try {
                                        return await navigator.clipboard.readText();
                                    } catch (e) {
                                        return null;
                                    }
                                }
                            """)
                            
                            if clipboard_test and len(clipboard_test) > 10:
                                print(f"✅ {selector} 复制成功: {clipboard_test[:100]}...")
                                break
                        
                except Exception as e:
                    print(f"❌ {selector} 失败: {e}")
            
            # 截图保存
            await bot.page.screenshot(path="icon_click_test.png", full_page=True)
            print("📸 测试截图已保存: icon_click_test.png")
            
            print("\n⏸️  浏览器将保持打开30秒...")
            await asyncio.sleep(30)
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    try:
        asyncio.run(test_icon_click())
        print("\n✅ 测试完成")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
