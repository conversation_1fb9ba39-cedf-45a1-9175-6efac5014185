#!/usr/bin/env python3
"""
简单测试消息发送和回复提取
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_simple_message():
    """测试简单消息"""
    print("📝 测试简单消息发送...")
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        keep_browser_open=True,  # 保持浏览器打开
        response_timeout=180000  # 180秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 测试消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 发送消息
            response = await bot.send_message_stealth(test_message)
            print(f"📥 收到回复: {response}")
            
            print("\n✅ 测试完成！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_simple_message())
