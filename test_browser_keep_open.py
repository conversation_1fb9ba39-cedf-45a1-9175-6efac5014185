#!/usr/bin/env python3
"""
测试浏览器保持打开功能
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_browser_keep_open():
    """测试浏览器保持打开功能"""
    print("🧪 测试浏览器保持打开功能")
    print("=" * 50)
    
    # 创建配置 - 保持浏览器打开
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        keep_browser_open=True,  # 保持浏览器打开
        response_timeout=30000  # 30秒超时
    )
    
    try:
        print("\n📝 第一次聊天...")
        async with StealthBaiduChatBot(config) as bot1:
            print("✅ 第一个聊天实例初始化成功")
            
            # 发送第一条消息
            try:
                response1 = await bot1.send_message_stealth("你好")
                print(f"📥 第一次回复: {response1[:50]}...")
            except Exception as e:
                print(f"❌ 第一次发送失败: {e}")
        
        print("\n⏸️  第一次聊天结束，浏览器应该保持打开...")
        print("请检查浏览器是否仍然打开")
        
        # 等待用户确认
        input("按回车键继续第二次聊天...")
        
        print("\n📝 第二次聊天（应该复用浏览器）...")
        async with StealthBaiduChatBot(config) as bot2:
            print("✅ 第二个聊天实例初始化成功（应该更快）")
            
            # 发送第二条消息
            try:
                response2 = await bot2.send_message_stealth("今天天气怎么样？")
                print(f"📥 第二次回复: {response2[:50]}...")
            except Exception as e:
                print(f"❌ 第二次发送失败: {e}")
        
        print("\n⏸️  第二次聊天结束，浏览器应该仍然保持打开...")
        print("请检查浏览器是否仍然打开")
        
        # 询问是否关闭浏览器
        close_browser = input("是否要关闭浏览器？(y/n): ").lower().strip()
        if close_browser in ['y', 'yes']:
            print("🔒 关闭浏览器...")
            await StealthBaiduChatBot.close_browser()
            print("✅ 浏览器已关闭")
        else:
            print("🌐 浏览器保持打开状态")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_browser_close():
    """测试浏览器关闭功能"""
    print("\n🧪 测试浏览器关闭功能")
    print("=" * 50)
    
    # 创建配置 - 不保持浏览器打开
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        keep_browser_open=False,  # 不保持浏览器打开
        response_timeout=30000  # 30秒超时
    )
    
    try:
        print("\n📝 聊天（应该在结束后关闭页面）...")
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 聊天实例初始化成功")
            
            # 发送消息
            try:
                response = await bot.send_message_stealth("你好")
                print(f"📥 回复: {response[:50]}...")
            except Exception as e:
                print(f"❌ 发送失败: {e}")
        
        print("\n⏸️  聊天结束，页面应该已关闭...")
        print("请检查浏览器页面是否已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def main():
    """主函数"""
    try:
        # 测试保持打开功能
        asyncio.run(test_browser_keep_open())
        
        # 测试关闭功能
        # asyncio.run(test_browser_close())
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
