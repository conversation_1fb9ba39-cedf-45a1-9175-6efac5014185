#!/usr/bin/env python3
"""
基于截图精确测试复制按钮的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_precise_copy_button():
    """精确测试复制按钮方案"""
    print("🎯 精确测试复制按钮方案")
    print("=" * 50)
    
    config = BaiduChatConfig(
        base_url="https://chat.baidu.com/",
        response_timeout=180000,
        keep_browser_open=True,
        debug=True
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送简单消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复...")
            
            # 等待回答完成
            await bot._wait_for_completion_status()
            print("✅ 回答完成，开始精确查找复制按钮...")
            
            # 基于截图的精确查找策略
            copy_button_found = False
            
            # 策略1: 查找AI回复区域下的按钮组
            print("\n🔍 策略1: 查找AI回复区域下的按钮组...")
            try:
                # 查找所有可能的AI回复容器
                ai_containers = await bot.page.query_selector_all(
                    'div[class*="ai-entry"], div[class*="answer"], div[class*="response"], div[class*="chat"]'
                )
                
                print(f"找到 {len(ai_containers)} 个AI回复容器")
                
                for i, container in enumerate(ai_containers):
                    try:
                        # 在每个容器中查找按钮
                        buttons = await container.query_selector_all('button')
                        if buttons:
                            print(f"容器 {i+1} 中找到 {len(buttons)} 个按钮")
                            
                            # 检查第一个按钮（通常是复制按钮）
                            first_button = buttons[0]
                            is_visible = await first_button.is_visible()
                            
                            if is_visible:
                                print(f"✅ 容器 {i+1} 的第一个按钮可见，尝试点击...")
                                
                                # 尝试点击
                                await first_button.scroll_into_view_if_needed()
                                await asyncio.sleep(1)
                                await first_button.click()
                                
                                print("✅ 按钮点击成功")
                                copy_button_found = True
                                break
                                
                    except Exception as e:
                        print(f"❌ 容器 {i+1} 处理失败: {e}")
                        continue
                        
            except Exception as e:
                print(f"❌ 策略1失败: {e}")
            
            # 策略2: 如果策略1失败，尝试悬停激活
            if not copy_button_found:
                print("\n🔍 策略2: 悬停激活复制按钮...")
                try:
                    # 查找最新的AI回复文本
                    response_texts = await bot.page.query_selector_all('div:has-text("你好呀"), div:has-text("很高兴")')
                    if response_texts:
                        last_response = response_texts[-1]
                        print("找到AI回复文本，开始悬停...")
                        
                        # 悬停激活
                        await last_response.hover()
                        await asyncio.sleep(2)
                        
                        # 重新查找按钮
                        nearby_buttons = await bot.page.query_selector_all('button')
                        print(f"悬停后找到 {len(nearby_buttons)} 个按钮")
                        
                        # 查找复制相关的按钮
                        for button in nearby_buttons:
                            try:
                                is_visible = await button.is_visible()
                                if is_visible:
                                    # 获取按钮的属性信息
                                    button_info = await button.evaluate("""
                                        (btn) => {
                                            return {
                                                className: btn.className,
                                                title: btn.title,
                                                ariaLabel: btn.getAttribute('aria-label'),
                                                innerHTML: btn.innerHTML
                                            };
                                        }
                                    """)
                                    
                                    # 检查是否是复制按钮
                                    if (button_info.get('title', '').find('复制') >= 0 or
                                        button_info.get('ariaLabel', '').find('复制') >= 0 or
                                        button_info.get('className', '').find('copy') >= 0 or
                                        button_info.get('innerHTML', '').find('copy') >= 0):
                                        
                                        print(f"✅ 找到复制按钮: {button_info}")
                                        await button.click()
                                        copy_button_found = True
                                        break
                                        
                            except Exception as e:
                                continue
                                
                except Exception as e:
                    print(f"❌ 策略2失败: {e}")
            
            # 测试剪贴板读取
            if copy_button_found:
                print("\n📋 测试剪贴板读取...")
                await asyncio.sleep(2)  # 等待复制完成
                
                # 尝试多种方法读取剪贴板
                clipboard_content = None
                
                # 方法1: 直接读取
                try:
                    clipboard_content = await bot.page.evaluate("""
                        async () => {
                            try {
                                return await navigator.clipboard.readText();
                            } catch (e) {
                                return null;
                            }
                        }
                    """)
                    if clipboard_content:
                        print(f"✅ 方法1成功: {clipboard_content[:100]}...")
                except:
                    pass
                
                # 方法2: 临时输入框粘贴
                if not clipboard_content:
                    try:
                        clipboard_content = await bot.page.evaluate("""
                            () => {
                                const tempInput = document.createElement('textarea');
                                tempInput.style.position = 'fixed';
                                tempInput.style.left = '-9999px';
                                document.body.appendChild(tempInput);
                                tempInput.focus();
                                document.execCommand('paste');
                                const content = tempInput.value;
                                document.body.removeChild(tempInput);
                                return content;
                            }
                        """)
                        if clipboard_content:
                            print(f"✅ 方法2成功: {clipboard_content[:100]}...")
                    except:
                        pass
                
                # 方法3: Ctrl+V模拟
                if not clipboard_content:
                    try:
                        await bot.page.evaluate("""
                            () => {
                                const tempDiv = document.createElement('div');
                                tempDiv.contentEditable = true;
                                tempDiv.id = 'temp-paste-area';
                                tempDiv.style.position = 'fixed';
                                tempDiv.style.left = '-9999px';
                                document.body.appendChild(tempDiv);
                                tempDiv.focus();
                            }
                        """)
                        
                        await bot.page.keyboard.press('Control+v')
                        await asyncio.sleep(1)
                        
                        clipboard_content = await bot.page.evaluate("""
                            () => {
                                const tempDiv = document.getElementById('temp-paste-area');
                                const content = tempDiv ? tempDiv.innerText : null;
                                if (tempDiv) document.body.removeChild(tempDiv);
                                return content;
                            }
                        """)
                        if clipboard_content:
                            print(f"✅ 方法3成功: {clipboard_content[:100]}...")
                    except:
                        pass
                
                if not clipboard_content:
                    print("❌ 所有剪贴板读取方法都失败")
            else:
                print("❌ 未找到复制按钮")
            
            # 截图保存
            await bot.page.screenshot(path="precise_copy_test.png", full_page=True)
            print("📸 测试截图已保存: precise_copy_test.png")
            
            # 保持浏览器打开
            print("\n⏸️  浏览器将保持打开30秒...")
            await asyncio.sleep(30)
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    try:
        asyncio.run(test_precise_copy_button())
        print("\n✅ 测试完成")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
