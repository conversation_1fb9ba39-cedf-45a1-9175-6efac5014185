#!/usr/bin/env python3
"""
详细调试页面内容 - 分析AI回复的实际位置和结构
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def debug_page_content():
    """详细调试页面内容"""
    print("🔍 详细调试页面内容...")
    print("=" * 60)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=180000  # 180秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复...")
            
            # 等待回答完成
            max_wait_time = 120
            completion_detected = False
            
            for wait_time in range(0, max_wait_time, 5):
                completion_status = await bot._check_completion_status()
                if completion_status:
                    print(f"✅ 在第{wait_time}秒检测到'回答完成'状态！")
                    completion_detected = True
                    break
                print(f"⏳ 等待回答完成... {wait_time}秒")
                await asyncio.sleep(5)
            
            if not completion_detected:
                print("❌ 未检测到'回答完成'状态")
                return
            
            # 等待内容完全加载
            await asyncio.sleep(3)
            
            print("\n🔍 分析页面结构...")
            
            # 1. 获取页面的完整文本内容
            page_text = await bot.page.evaluate("() => document.body.innerText")
            print(f"\n📄 页面完整文本内容:")
            print("=" * 40)
            lines = page_text.split('\n')
            for i, line in enumerate(lines[:50], 1):  # 显示前50行
                if line.strip():
                    print(f"{i:2d}: {line.strip()}")
            
            # 2. 查找所有可能的AI回复元素
            print(f"\n🎯 查找所有可能的AI回复元素...")
            print("=" * 40)
            
            all_elements = await bot.page.evaluate("""
                () => {
                    const elements = [];
                    const allNodes = document.querySelectorAll('*');
                    
                    for (let node of allNodes) {
                        const text = node.innerText || '';
                        if (text && text.trim().length > 0 && text.trim().length < 1000) {
                            elements.push({
                                tagName: node.tagName,
                                className: node.className || '',
                                id: node.id || '',
                                text: text.trim(),
                                textLength: text.trim().length,
                                hasChildren: node.children.length > 0
                            });
                        }
                    }
                    
                    // 按文本长度排序
                    elements.sort((a, b) => a.textLength - b.textLength);
                    return elements;
                }
            """)
            
            print(f"找到 {len(all_elements)} 个包含文本的元素")
            
            # 3. 分析可能的AI回复
            print(f"\n📊 分析可能的AI回复（按长度分类）:")
            print("=" * 40)
            
            short_texts = []  # 2-20字符
            medium_texts = []  # 21-200字符
            long_texts = []   # 200+字符
            
            for elem in all_elements:
                text = elem['text']
                length = elem['textLength']
                
                if 2 <= length <= 20:
                    short_texts.append(elem)
                elif 21 <= length <= 200:
                    medium_texts.append(elem)
                elif length > 200:
                    long_texts.append(elem)
            
            print(f"\n🔸 短文本 (2-20字符): {len(short_texts)} 个")
            for i, elem in enumerate(short_texts[:10], 1):
                print(f"  {i}. [{elem['tagName']}] '{elem['text']}' (长度: {elem['textLength']})")
            
            print(f"\n🔹 中等文本 (21-200字符): {len(medium_texts)} 个")
            for i, elem in enumerate(medium_texts[:10], 1):
                print(f"  {i}. [{elem['tagName']}] '{elem['text'][:50]}...' (长度: {elem['textLength']})")
            
            print(f"\n🔺 长文本 (200+字符): {len(long_texts)} 个")
            for i, elem in enumerate(long_texts[:5], 1):
                print(f"  {i}. [{elem['tagName']}] '{elem['text'][:80]}...' (长度: {elem['textLength']})")
            
            # 4. 查找包含特定关键词的元素
            print(f"\n🎯 查找包含AI回复关键词的元素:")
            print("=" * 40)
            
            keywords = ['你好', '呀', '很高兴', '我可以', '帮助', 'DeepSeek', 'AI']
            
            for keyword in keywords:
                matching_elements = [elem for elem in all_elements if keyword in elem['text']]
                if matching_elements:
                    print(f"\n包含 '{keyword}' 的元素 ({len(matching_elements)} 个):")
                    for i, elem in enumerate(matching_elements[:3], 1):
                        print(f"  {i}. [{elem['tagName']}] '{elem['text'][:60]}...'")
            
            # 5. 保持浏览器打开供手动检查
            print(f"\n⏸️  浏览器将保持打开60秒，请手动检查页面...")
            print("请在浏览器中查看AI的实际回复内容，并记录其特征")
            await asyncio.sleep(60)
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(debug_page_content())
