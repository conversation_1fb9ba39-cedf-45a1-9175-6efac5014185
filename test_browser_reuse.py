#!/usr/bin/env python3
"""
测试浏览器复用功能
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_browser_reuse():
    """测试浏览器复用功能"""
    print("🔄 测试浏览器复用功能...")
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=30000  # 30秒超时
    )
    
    try:
        print("\n📝 第一次聊天会话...")
        async with StealthBaiduChatBot(config) as bot1:
            print("✅ 第一个聊天实例初始化成功")
            
            # 发送第一条消息
            response1 = await bot1.send_message_stealth("你好")
            print(f"📥 第一次回复: {response1[:50]}...")
        
        print("\n📝 第二次聊天会话（应该复用浏览器）...")
        async with StealthBaiduChatBot(config) as bot2:
            print("✅ 第二个聊天实例初始化成功（应该更快）")
            
            # 发送第二条消息
            response2 = await bot2.send_message_stealth("今天天气怎么样？")
            print(f"📥 第二次回复: {response2[:50]}...")
        
        print("\n📝 第三次聊天会话...")
        async with StealthBaiduChatBot(config) as bot3:
            print("✅ 第三个聊天实例初始化成功")
            
            # 发送第三条消息
            response3 = await bot3.send_message_stealth("谢谢")
            print(f"📥 第三次回复: {response3[:50]}...")
        
        print("\n🎉 浏览器复用测试完成！")
        print("💡 注意：浏览器应该保持打开状态，只是创建了新的页面")
        
        # 询问是否关闭浏览器
        print("\n❓ 是否要关闭浏览器？(y/n)")
        # 在自动化测试中，我们直接关闭
        print("🔒 自动关闭浏览器...")
        await StealthBaiduChatBot.close_browser()
        print("✅ 浏览器已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_quick_multiple_chats():
    """测试快速多次聊天"""
    print("\n🚀 测试快速多次聊天...")
    
    config = BaiduChatConfig(
        headless=False,
        debug=False,  # 减少日志输出
        response_timeout=20000
    )
    
    messages = [
        "你好",
        "今天天气怎么样？",
        "推荐一本好书",
        "谢谢你的建议"
    ]
    
    try:
        for i, message in enumerate(messages, 1):
            print(f"\n💬 第{i}次对话: {message}")
            
            async with StealthBaiduChatBot(config) as bot:
                response = await bot.send_message_stealth(message)
                print(f"🤖 回复: {response[:80]}...")
        
        print("\n🎉 快速多次聊天测试完成！")
        
        # 关闭浏览器
        await StealthBaiduChatBot.close_browser()
        
    except Exception as e:
        print(f"❌ 快速聊天测试失败: {e}")


def main():
    """主函数"""
    print("🧪 浏览器复用功能测试")
    print("=" * 50)
    
    try:
        # 运行浏览器复用测试
        asyncio.run(test_browser_reuse())
        
        # 运行快速多次聊天测试
        # asyncio.run(test_quick_multiple_chats())
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
