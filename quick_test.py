#!/usr/bin/env python3
"""
快速测试修复后的功能
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def quick_test():
    """快速测试"""
    print("🚀 快速测试修复后的AI回复识别...")
    
    # 创建配置 - 使用更短的超时时间
    config = BaiduChatConfig(
        headless=True,   # 无头模式，更快
        debug=False,     # 关闭调试日志
        response_timeout=20000,  # 20秒超时
        page_load_timeout=15000  # 15秒页面加载超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送简单消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            response = await bot.send_message_stealth(test_message)
            print(f"📥 收到回复: {response}")
            
            # 验证回复
            if bot._is_valid_ai_response(response):
                print("✅ 回复验证通过 - 这是有效的AI回复")
                print("🎉 修复成功！程序现在能正确识别AI回复了")
            else:
                print("❌ 回复验证失败 - 仍然获取到了错误的内容")
                print(f"❌ 获取到的内容: {response}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def main():
    """主函数"""
    try:
        asyncio.run(quick_test())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
