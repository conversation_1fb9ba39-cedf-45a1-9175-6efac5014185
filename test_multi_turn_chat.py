#!/usr/bin/env python3
"""
测试多轮对话功能
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_multi_turn_chat():
    """测试多轮对话"""
    print("🔄 测试多轮对话功能...")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        keep_browser_open=True,  # 保持浏览器打开
        response_timeout=180000  # 180秒超时
    )
    
    # 测试问题列表
    test_questions = [
        "你好，请简单介绍一下你自己",
        "你能帮我解释一下什么是人工智能吗？",
        "深圳有哪些著名的景点？",
        "请推荐几道简单易做的家常菜",
        "谢谢你的回答"
    ]
    
    try:
        print("🚀 初始化隐蔽模式机器人...")
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 隐蔽模式机器人初始化成功")
            
            for i, question in enumerate(test_questions, 1):
                print(f"\n📤 第{i}轮对话 - 发送消息: {question}")
                
                try:
                    # 发送消息
                    response = await bot.send_message_stealth(question)
                    print(f"📥 第{i}轮对话 - 收到回复: {response[:100]}...")
                    
                    # 检查回复是否与之前的回复重复
                    if i > 1:
                        history = bot.get_conversation_history()
                        if len(history) >= 2:
                            current_response = history[-1]['ai_response']
                            previous_response = history[-2]['ai_response']
                            
                            if current_response == previous_response:
                                print(f"⚠️  警告: 第{i}轮回复与第{i-1}轮回复相同!")
                                print(f"当前回复: {current_response[:50]}...")
                                print(f"上轮回复: {previous_response[:50]}...")
                            else:
                                print(f"✅ 第{i}轮回复正常，与上轮不同")
                    
                    # 等待一下再发送下一个问题
                    if i < len(test_questions):
                        print("⏳ 等待3秒后发送下一个问题...")
                        await asyncio.sleep(3)
                        
                except Exception as e:
                    print(f"❌ 第{i}轮对话失败: {e}")
                    continue
            
            # 显示完整的对话历史
            print("\n📚 完整对话历史:")
            print("=" * 50)
            history = bot.get_conversation_history()
            for i, conversation in enumerate(history, 1):
                user_msg = conversation.get('user_message', '')
                ai_msg = conversation.get('ai_response', '')
                print(f"\n第{i}轮:")
                print(f"用户: {user_msg}")
                print(f"AI: {ai_msg[:100]}...")
            
            print(f"\n✅ 多轮对话测试完成！共进行了 {len(history)} 轮对话")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_multi_turn_chat())
