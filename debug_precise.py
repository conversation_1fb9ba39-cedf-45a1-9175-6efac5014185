#!/usr/bin/env python3
"""
精确调试 - 查找页面上的具体回复内容
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def debug_precise_content():
    """精确调试页面内容"""
    print("🎯 精确调试页面回复内容...")
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=60000  # 60秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复...")

            # 循环等待，直到检测到回复或超时
            max_wait_time = 60  # 最多等待60秒
            check_interval = 3   # 每3秒检查一次

            for wait_time in range(0, max_wait_time, check_interval):
                print(f"⏰ 等待中... {wait_time}秒")

                # 检查是否有"回答完成"
                page_content = await bot.page.content()
                if "回答完成" in page_content or "你好呀" in page_content:
                    print(f"✅ 在第{wait_time}秒检测到回复内容！")
                    break

                await asyncio.sleep(check_interval)
            else:
                print("⚠️ 等待超时，但继续分析页面内容...")
            
            print("\n🔍 开始精确分析页面内容...")
            
            # 1. 检查页面是否包含"回答完成"
            page_content = await bot.page.content()
            if "回答完成" in page_content:
                print("✅ 页面包含'回答完成'文本")
            else:
                print("❌ 页面不包含'回答完成'文本")
            
            # 2. 查找包含"你好呀"的元素
            print("\n🔍 查找包含'你好呀'的元素...")
            try:
                # 使用JavaScript在页面中查找包含特定文本的元素
                elements_with_hello = await bot.page.evaluate("""
                    () => {
                        const allElements = document.querySelectorAll('*');
                        const results = [];
                        
                        for (let element of allElements) {
                            const text = element.innerText || element.textContent || '';
                            if (text.includes('你好呀') || text.includes('很高兴见到你')) {
                                results.push({
                                    tagName: element.tagName,
                                    className: element.className,
                                    id: element.id,
                                    text: text.substring(0, 200),
                                    fullText: text
                                });
                            }
                        }
                        return results;
                    }
                """)
                
                if elements_with_hello:
                    print(f"✅ 找到 {len(elements_with_hello)} 个包含回复内容的元素:")
                    for i, elem in enumerate(elements_with_hello, 1):
                        print(f"\n[{i}] 元素信息:")
                        print(f"    标签: {elem['tagName']}")
                        print(f"    类名: {elem['className']}")
                        print(f"    ID: {elem['id']}")
                        print(f"    文本预览: {elem['text']}")
                        
                        # 验证这是否是有效的AI回复
                        is_valid = bot._is_valid_ai_response(elem['fullText'])
                        print(f"    有效性检查: {'✅ 有效' if is_valid else '❌ 无效'}")
                        
                        if is_valid:
                            print(f"    🎉 这是有效的AI回复！")
                            print(f"    完整内容: {elem['fullText']}")
                            return elem['fullText']
                else:
                    print("❌ 没有找到包含回复内容的元素")
            
            except Exception as e:
                print(f"❌ JavaScript查找失败: {e}")
            
            # 3. 尝试我们现有的选择器
            print("\n🔍 测试现有选择器...")
            response_selectors = [
                '.chat-item:last-child',
                '.message-item:last-child',
                'div[class*="chat"]:last-child',
                'div[class*="message"]:last-child',
                '[class*="answer"]'
            ]
            
            for selector in response_selectors:
                try:
                    elements = await bot.page.query_selector_all(selector)
                    if elements:
                        print(f"\n选择器 '{selector}' 找到 {len(elements)} 个元素:")
                        for i, element in enumerate(elements[-3:], 1):  # 检查最后3个
                            text = await element.inner_text()
                            if text and len(text.strip()) > 0:
                                print(f"  [{i}] 文本: {text[:100]}...")
                                if bot._is_valid_ai_response(text):
                                    print(f"  ✅ 这是有效的AI回复！")
                                    return text
                except Exception as e:
                    print(f"选择器 '{selector}' 出错: {e}")
            
            # 4. 最后尝试：获取页面上所有可见文本
            print("\n🔍 获取页面所有可见文本...")
            try:
                all_text = await bot.page.evaluate("""
                    () => {
                        return document.body.innerText;
                    }
                """)
                
                # 查找包含AI回复特征的文本段落
                lines = all_text.split('\n')
                for i, line in enumerate(lines):
                    if '你好呀' in line or '很高兴见到你' in line:
                        print(f"✅ 在第{i+1}行找到回复内容:")
                        # 获取这一行及其前后几行
                        context_start = max(0, i-2)
                        context_end = min(len(lines), i+5)
                        context = '\n'.join(lines[context_start:context_end])
                        print(f"上下文:\n{context}")
                        
                        if bot._is_valid_ai_response(context):
                            print("✅ 上下文是有效的AI回复！")
                            return context
                        
            except Exception as e:
                print(f"❌ 获取页面文本失败: {e}")
            
            print("❌ 未能找到有效的AI回复")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    try:
        result = asyncio.run(debug_precise_content())
        if result:
            print(f"\n🎉 最终找到的AI回复:\n{result}")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试程序异常: {e}")


if __name__ == "__main__":
    main()
