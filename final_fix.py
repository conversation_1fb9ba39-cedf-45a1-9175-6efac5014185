#!/usr/bin/env python3
"""
最终修复方案 - 基于调试结果的简化解决方案
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def simple_chat_test():
    """简化的聊天测试"""
    print("🔧 最终修复方案测试")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=60000  # 60秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，开始等待回复...")
            
            # 简化的等待逻辑 - 基于我们的调试发现
            max_wait_time = 60  # 最多等待60秒
            check_interval = 2   # 每2秒检查一次
            
            for wait_time in range(0, max_wait_time, check_interval):
                print(f"⏰ 等待中... {wait_time}秒")
                
                try:
                    # 使用我们调试中发现的有效方法
                    ai_response = await bot.page.evaluate("""
                        () => {
                            // 查找所有div元素
                            const allDivs = document.querySelectorAll('div');
                            const candidates = [];
                            
                            for (let div of allDivs) {
                                const text = div.innerText || '';
                                
                                // 基于调试结果的精确匹配条件
                                if (text.length > 20 && text.length < 500 &&
                                    (text.includes('你好呀') || text.includes('很高兴') || 
                                     text.includes('我可以') || text.includes('帮助你') ||
                                     text.includes('👋') || text.includes('😊')) &&
                                    !text.includes('DeepSeek-R1满血版') &&
                                    !text.includes('回答完成') &&
                                    !text.includes('思考完成') &&
                                    !text.includes('提问') &&
                                    !text.includes('发送') &&
                                    !text.includes('搜索') &&
                                    !text.includes('百度首页')) {
                                    
                                    candidates.push({
                                        text: text.trim(),
                                        length: text.length
                                    });
                                }
                            }
                            
                            // 返回最短的候选项（最纯净的AI回复）
                            if (candidates.length > 0) {
                                candidates.sort((a, b) => a.length - b.length);
                                return candidates[0].text;
                            }
                            
                            return null;
                        }
                    """)
                    
                    if ai_response and len(ai_response.strip()) > 10:
                        print(f"✅ 找到AI回复!")
                        print(f"📥 回复内容: {ai_response}")
                        return ai_response
                        
                except Exception as e:
                    print(f"⚠️ 检查时出错: {e}")
                
                await asyncio.sleep(check_interval)
            
            print("❌ 等待超时，未找到AI回复")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    try:
        result = asyncio.run(simple_chat_test())
        if result:
            print(f"\n🎉 成功获取AI回复!")
            print("✅ 修复方案有效!")
        else:
            print(f"\n❌ 未能获取AI回复")
            print("❌ 需要进一步调试")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
