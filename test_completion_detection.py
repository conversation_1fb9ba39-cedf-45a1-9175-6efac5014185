#!/usr/bin/env python3
"""
测试"回答完成"状态检测
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_completion_detection():
    """测试完成状态检测"""
    print("🔍 测试回答完成状态检测")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=120000  # 120秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，开始监控状态...")
            
            # 监控状态变化
            max_wait_time = 120  # 最多等待120秒
            check_interval = 2   # 每2秒检查一次
            
            for wait_time in range(0, max_wait_time, check_interval):
                print(f"\n⏰ 等待时间: {wait_time}秒")
                
                try:
                    # 检查完成状态
                    completion_status = await bot._check_completion_status()
                    print(f"🔍 完成状态检测: {completion_status}")
                    
                    # 获取页面内容来查看状态
                    page_content = await bot.page.content()
                    
                    # 检查关键状态文本
                    status_indicators = [
                        "回答完成",
                        "思考完成", 
                        "DeepSeek-R1满血版 回答完成",
                        "DeepSeek-R1满血版 思考完成",
                        "思考中",
                        "DeepSeek-R1满血版 思考中"
                    ]
                    
                    found_indicators = []
                    for indicator in status_indicators:
                        if indicator in page_content:
                            found_indicators.append(indicator)
                    
                    if found_indicators:
                        print(f"📊 发现状态指示器: {found_indicators}")
                        
                        # 如果发现完成状态
                        if any("完成" in indicator for indicator in found_indicators):
                            print("🎉 检测到完成状态！")
                            
                            # 现在尝试提取AI回复
                            print("\n🔍 开始提取AI回复内容...")
                            
                            ai_response = await bot.page.evaluate("""
                                () => {
                                    const allDivs = document.querySelectorAll('div');
                                    const candidates = [];

                                    for (let div of allDivs) {
                                        const text = div.innerText || '';

                                        // 更严格的过滤条件
                                        if (text.length > 20 && text.length < 500 &&
                                            !text.includes('DeepSeek-R1满血版') &&
                                            !text.includes('回答完成') &&
                                            !text.includes('思考完成') &&
                                            !text.includes('提问') &&
                                            !text.includes('发送') &&
                                            !text.includes('搜索') &&
                                            !text.includes('登录') &&
                                            !text.includes('注册') &&
                                            !text.includes('百度首页') &&
                                            !text.includes('网页') &&
                                            !text.includes('图片') &&
                                            !text.includes('资讯') &&
                                            !text.includes('视频') &&
                                            !text.includes('笔记') &&
                                            !text.includes('地图') &&
                                            !text.includes('贴吧') &&
                                            !text.includes('文库') &&
                                            !text.includes('深度思考') &&
                                            !text.includes('快速回答') &&
                                            !text.includes('深度搜索') &&
                                            !text.includes('智能体') &&
                                            !text.includes('反馈') &&
                                            !text.includes('快捷入口') &&
                                            !text.includes('开启新对话') &&
                                            !text.includes('知识库') &&
                                            !text.includes('对话') &&
                                            !text.includes('更多') &&
                                            !text.includes('问问智能体') &&
                                            !text.includes('绝招') &&
                                            !text.includes('躺平') &&
                                            !text.includes('？')) {

                                            // 检查是否包含AI回复的特征
                                            const hasAiFeatures = (
                                                text.includes('你好呀') ||
                                                text.includes('很高兴见到你') ||
                                                text.includes('我可以帮你') ||
                                                text.includes('帮助你') ||
                                                text.includes('我是') ||
                                                text.includes('👋') ||
                                                text.includes('😊') ||
                                                text.includes('✨') ||
                                                text.includes('🌟')
                                            );

                                            if (hasAiFeatures) {
                                                candidates.push({
                                                    text: text.trim(),
                                                    length: text.length
                                                });
                                            }
                                        }
                                    }

                                    // 返回最短的候选项（通常是最纯净的AI回复）
                                    if (candidates.length > 0) {
                                        candidates.sort((a, b) => a.length - b.length);
                                        return candidates[0].text;
                                    }

                                    return null;
                                }
                            """)
                            
                            if ai_response:
                                print(f"✅ 成功提取AI回复:")
                                print(f"📥 {ai_response}")
                                return ai_response
                            else:
                                print("❌ 未能提取到AI回复内容")
                    else:
                        print("📊 未发现状态指示器")
                    
                except Exception as e:
                    print(f"❌ 检查时出错: {e}")
                
                await asyncio.sleep(check_interval)
            
            print("❌ 监控超时")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    try:
        result = asyncio.run(test_completion_detection())
        if result:
            print(f"\n🎉 测试成功！获取到AI回复")
        else:
            print(f"\n❌ 测试失败，未获取到AI回复")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
