    async def _extract_ai_response(self) -> str:
        """提取AI回复内容 - 通用版本，适用于任何问题"""
        try:
            # 策略1: 基于"回答完成"定位的通用方法
            ai_response = await self.page.evaluate("""
                () => {
                    // 查找包含"回答完成"的元素
                    const allElements = document.querySelectorAll('*');
                    let completionElement = null;
                    
                    for (let element of allElements) {
                        const text = element.innerText || '';
                        if (text.includes('回答完成')) {
                            completionElement = element;
                            break;
                        }
                    }
                    
                    if (completionElement) {
                        // 查找"回答完成"元素后面的内容
                        let current = completionElement;
                        
                        // 向上查找父元素，然后查找后续的兄弟元素
                        while (current && current.parentElement) {
                            const parent = current.parentElement;
                            const siblings = Array.from(parent.children);
                            const currentIndex = siblings.indexOf(current);
                            
                            // 查找当前元素后面的兄弟元素
                            for (let i = currentIndex + 1; i < siblings.length; i++) {
                                const sibling = siblings[i];
                                const siblingText = sibling.innerText || '';
                                
                                // 通用条件：长度合适且不是界面元素
                                if (siblingText.length > 10 && siblingText.length < 5000 &&
                                    !siblingText.includes('DeepSeek-R1满血版') &&
                                    !siblingText.includes('回答完成') &&
                                    !siblingText.includes('思考完成') &&
                                    !siblingText.includes('思考中') &&
                                    !siblingText.includes('登录') &&
                                    !siblingText.includes('注册') &&
                                    !siblingText.includes('百度首页') &&
                                    !siblingText.includes('发送') &&
                                    !siblingText.includes('提问') &&
                                    !siblingText.includes('搜索')) {
                                    
                                    console.log('策略1找到回答完成后的内容:', siblingText.substring(0, 100));
                                    return siblingText.trim();
                                }
                            }
                            
                            current = parent;
                        }
                    }
                    
                    return null;
                }
            """)

            if ai_response and len(ai_response.strip()) > 10:
                cleaned_response = ai_response.strip()
                self.logger.debug(f"策略1成功提取: {cleaned_response[:100]}...")
                return cleaned_response

            # 策略2: 查找最新的、长度合适的文本内容
            ai_response = await self.page.evaluate("""
                () => {
                    const allElements = document.querySelectorAll('div, p, span');
                    const candidates = [];

                    for (let i = 0; i < allElements.length; i++) {
                        const element = allElements[i];
                        const text = element.innerText || '';

                        // 查找长度合适的文本，排除界面元素
                        if (text.length > 20 && text.length < 3000 &&
                            !text.includes('DeepSeek-R1满血版') &&
                            !text.includes('回答完成') &&
                            !text.includes('思考完成') &&
                            !text.includes('思考中') &&
                            !text.includes('登录') &&
                            !text.includes('注册') &&
                            !text.includes('百度首页') &&
                            !text.includes('网页') &&
                            !text.includes('图片') &&
                            !text.includes('视频') &&
                            !text.includes('贴吧') &&
                            !text.includes('文库') &&
                            !text.includes('地图') &&
                            !text.includes('笔记') &&
                            !text.includes('发送') &&
                            !text.includes('提问') &&
                            !text.includes('搜索') &&
                            !text.includes('开启新对话') &&
                            !text.includes('知识库') &&
                            !text.includes('智能体') &&
                            !text.includes('反馈') &&
                            !text.includes('快捷入口')) {
                            
                            candidates.push({
                                text: text.trim(),
                                length: text.length,
                                index: i  // 记录元素位置
                            });
                        }
                    }

                    if (candidates.length > 0) {
                        // 按位置排序，选择最后面的几个候选
                        candidates.sort((a, b) => b.index - a.index);
                        
                        // 在最后的几个候选中，选择长度最合适的
                        const topCandidates = candidates.slice(0, 5);
                        topCandidates.sort((a, b) => {
                            // 优先选择长度在50-800字符之间的
                            const aScore = Math.abs(a.length - 200);
                            const bScore = Math.abs(b.length - 200);
                            return aScore - bScore;
                        });
                        
                        console.log('策略2找到候选:', topCandidates[0].text.substring(0, 100));
                        return topCandidates[0].text;
                    }

                    return null;
                }
            """)

            if ai_response and len(ai_response.strip()) > 10:
                cleaned_response = ai_response.strip()
                self.logger.debug(f"策略2成功提取: {cleaned_response[:100]}...")
                return cleaned_response

            # 策略3: 最宽松的方法 - 查找任何合理长度的文本
            ai_response = await self.page.evaluate("""
                () => {
                    const allElements = document.querySelectorAll('*');
                    const candidates = [];

                    for (let element of allElements) {
                        const text = element.innerText || '';

                        // 最宽松的条件
                        if (text.length > 15 && text.length < 2000 &&
                            !text.includes('DeepSeek-R1满血版') &&
                            !text.includes('思考中') &&
                            !text.includes('登录') &&
                            !text.includes('注册') &&
                            !text.includes('百度首页')) {
                            
                            candidates.push(text.trim());
                        }
                    }

                    // 返回最长的候选（通常是最完整的内容）
                    if (candidates.length > 0) {
                        candidates.sort((a, b) => b.length - a.length);
                        console.log('策略3找到候选:', candidates[0].substring(0, 100));
                        return candidates[0];
                    }

                    return null;
                }
            """)

            if ai_response and len(ai_response.strip()) > 10:
                cleaned_response = ai_response.strip()
                self.logger.debug(f"策略3成功提取: {cleaned_response[:100]}...")
                return cleaned_response
            
            raise RuntimeError("无法提取有效的AI回复内容")
            
        except Exception as e:
            self.logger.error(f"提取AI回复失败: {e}")
            raise
