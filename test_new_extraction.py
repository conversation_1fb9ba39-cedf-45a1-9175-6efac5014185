#!/usr/bin/env python3
"""
测试新的AI回复提取逻辑
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_new_extraction():
    """测试新的提取逻辑"""
    print("🔧 测试新的AI回复提取逻辑...")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=180000  # 180秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 测试消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 发送消息并获取回复
            response = await bot.send_message_stealth(test_message)
            
            print(f"\n📥 收到回复:")
            print("=" * 40)
            print(response)
            print("=" * 40)
            
            # 验证回复内容
            expected_keywords = ['你好呀', '很高兴', '👋', '😊', '帮你', '解答', '建议']
            found_keywords = [kw for kw in expected_keywords if kw in response]
            
            print(f"\n🎯 回复分析:")
            print(f"回复长度: {len(response)} 字符")
            print(f"包含的关键词: {found_keywords}")
            
            if len(found_keywords) >= 3:
                print("🎉 成功！提取到了完整的AI回复内容！")
            elif len(found_keywords) >= 1:
                print("⚠️  部分成功，提取到了部分AI回复内容")
            else:
                print("❌ 提取失败，没有找到预期的AI回复内容")
            
            print("\n✅ 测试完成！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_new_extraction())
