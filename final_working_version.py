#!/usr/bin/env python3
"""
最终工作版本 - 基于我们成功的调试结果
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def working_chat(message: str):
    """工作的聊天函数"""
    print(f"📤 发送消息: {message}")
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        keep_browser_open=True,  # 保持浏览器打开
        response_timeout=60000  # 60秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 手动执行发送过程
            await bot._input_message_stealth(message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复完成...")
            
            # 等待回答完成
            max_wait_time = 60
            for wait_time in range(0, max_wait_time, 2):
                completion_status = await bot._check_completion_status()
                if completion_status:
                    print(f"✅ 在第{wait_time}秒检测到回答完成！")
                    break
                await asyncio.sleep(2)
            else:
                print("❌ 未检测到回答完成状态")
                return None
            
            # 等待一下确保内容完全加载
            await asyncio.sleep(3)
            
            print("🔍 开始提取AI回复...")
            
            # 使用我们之前成功的方法
            ai_response = await bot.page.evaluate("""
                () => {
                    const allDivs = document.querySelectorAll('div');
                    const candidates = [];

                    for (let div of allDivs) {
                        const text = div.innerText || '';
                        
                        // 查找包含AI回复特征且长度适中的元素
                        if (text.length > 30 && text.length < 500 &&
                            (text.includes('你好呀') || text.includes('很高兴') || 
                             text.includes('我可以帮') || text.includes('👋') || 
                             text.includes('😊')) &&
                            !text.includes('DeepSeek-R1满血版') &&
                            !text.includes('回答完成') &&
                            !text.includes('思考完成') &&
                            !text.includes('网页') &&
                            !text.includes('图片') &&
                            !text.includes('搜索') &&
                            !text.includes('登录') &&
                            !text.includes('注册') &&
                            !text.includes('百度首页') &&
                            !text.includes('视频') &&
                            !text.includes('笔记') &&
                            !text.includes('地图') &&
                            !text.includes('贴吧') &&
                            !text.includes('文库')) {
                            
                            candidates.push({
                                text: text.trim(),
                                length: text.length
                            });
                        }
                    }

                    // 返回最短的候选项（最纯净的AI回复）
                    if (candidates.length > 0) {
                        candidates.sort((a, b) => a.length - b.length);
                        return candidates[0].text;
                    }

                    return null;
                }
            """)
            
            if ai_response:
                print(f"✅ 成功提取AI回复:")
                print(f"📥 {ai_response}")
                return ai_response
            else:
                print("❌ 未能提取到AI回复")
                return None
                
    except Exception as e:
        print(f"❌ 聊天失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python final_working_version.py '你的消息'")
        sys.exit(1)
    
    message = sys.argv[1]
    
    try:
        result = asyncio.run(working_chat(message))
        if result:
            print(f"\n🎉 聊天成功！")
            print("💡 浏览器保持打开状态，可以继续使用")
        else:
            print(f"\n❌ 聊天失败")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")


if __name__ == "__main__":
    main()
