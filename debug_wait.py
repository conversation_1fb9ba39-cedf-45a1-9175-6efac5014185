#!/usr/bin/env python3
"""
调试等待AI回复的过程
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def debug_wait_process():
    """调试等待过程"""
    print("🐛 调试AI回复等待过程...")
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=60000  # 60秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，开始等待回复...")
            
            # 手动调试等待过程
            response_selectors = [
                '.chat-item:last-child .chat-content',
                '.chat-item:last-child',
                '.message-item:last-child .content',
                '.message-item:last-child',
                '.response-text',
                '.ai-message',
                '.bot-message',
                '.chat-answer',
                '.answer-content',
                '.chat-message:last-child',
                '.message:last-child',
                '.response:last-child',
                '.reply:last-child',
                'div[class*="chat"]:last-child',
                'div[class*="message"]:last-child',
                'div[class*="response"]:last-child',
                '[class*="answer"]'
            ]
            
            start_time = asyncio.get_event_loop().time()
            timeout = 60  # 60秒
            
            while (asyncio.get_event_loop().time() - start_time) < timeout:
                print(f"\n⏰ 等待时间: {int(asyncio.get_event_loop().time() - start_time)}秒")
                
                found_elements = []
                for selector in response_selectors:
                    try:
                        elements = await bot.page.query_selector_all(selector)
                        if elements:
                            for i, element in enumerate(elements[-3:]):  # 检查最后3个
                                try:
                                    text = await element.inner_text()
                                    if text and len(text.strip()) > 0:
                                        found_elements.append({
                                            'selector': selector,
                                            'text': text.strip()[:100],
                                            'full_text': text.strip()
                                        })
                                except:
                                    pass
                    except:
                        pass
                
                if found_elements:
                    print(f"🔍 找到 {len(found_elements)} 个元素:")
                    for elem in found_elements[-5:]:  # 显示最后5个
                        print(f"  选择器: {elem['selector']}")
                        print(f"  文本: {elem['text']}")
                        
                        # 检查是否是思考中
                        if ("思考中" in elem['full_text'] or 
                            "thinking" in elem['full_text'].lower() or
                            "DeepSeek-R1满血版 思考中" in elem['full_text']):
                            print("  🤔 检测到AI正在思考...")
                        
                        # 检查是否是有效回复
                        elif bot._is_valid_ai_response(elem['full_text']):
                            print("  ✅ 这是有效的AI回复！")
                            print(f"  完整回复: {elem['full_text']}")
                            return elem['full_text']
                        else:
                            print("  ❌ 不是有效的AI回复")
                        print()
                
                await asyncio.sleep(3)
            
            print("❌ 等待超时")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    try:
        asyncio.run(debug_wait_process())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试程序异常: {e}")


if __name__ == "__main__":
    main()
