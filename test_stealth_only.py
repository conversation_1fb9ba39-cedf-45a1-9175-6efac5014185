#!/usr/bin/env python3
"""
测试隐蔽模式专版功能
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_stealth_only():
    """测试隐蔽模式专版"""
    print("🥷 测试隐蔽模式专版功能...")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        keep_browser_open=True,  # 保持浏览器打开
        response_timeout=180000  # 180秒超时
    )
    
    try:
        print("🚀 初始化隐蔽模式机器人...")
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 隐蔽模式机器人初始化成功")
            
            # 测试消息
            test_message = "你好，请简单介绍一下你自己"
            print(f"📤 发送消息: {test_message}")
            
            # 发送消息
            response = await bot.send_message_stealth(test_message)
            print(f"📥 收到回复: {response[:100]}...")
            
            print("\n✅ 隐蔽模式测试完成！")
            print("🎉 所有普通模式代码已成功移除，现在只支持隐蔽模式")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_stealth_only())
