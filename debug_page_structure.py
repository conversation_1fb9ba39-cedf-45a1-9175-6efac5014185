#!/usr/bin/env python3
"""
详细分析页面结构的调试脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def debug_page_structure():
    """详细分析页面结构"""
    print("🔍 详细页面结构分析")
    print("=" * 50)
    
    config = BaiduChatConfig(
        base_url="https://chat.baidu.com/",
        keep_browser_open=True,
        debug=True
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复...")
            await bot._wait_for_completion_status()
            print("✅ 回答完成，开始分析页面结构...")
            
            # 分析页面结构
            page_analysis = await bot.page.evaluate("""
                () => {
                    const analysis = {
                        allElements: [],
                        buttonsInfo: [],
                        aiResponseAreas: [],
                        copyRelatedElements: []
                    };
                    
                    // 1. 查找所有包含AI回复的元素
                    const allDivs = document.querySelectorAll('div');
                    for (let div of allDivs) {
                        const text = div.innerText || '';
                        if (text.includes('你好呀') || text.includes('很高兴') || 
                            text.includes('DeepSeek') || text.includes('回答完成')) {
                            
                            analysis.aiResponseAreas.push({
                                tagName: div.tagName,
                                className: div.className,
                                id: div.id,
                                textContent: text.substring(0, 100),
                                hasButtons: div.querySelectorAll('button').length > 0,
                                buttonCount: div.querySelectorAll('button').length,
                                parentClassName: div.parentElement ? div.parentElement.className : '',
                                childrenCount: div.children.length
                            });
                        }
                    }
                    
                    // 2. 查找所有按钮
                    const allButtons = document.querySelectorAll('button');
                    for (let button of allButtons) {
                        const buttonInfo = {
                            className: button.className,
                            id: button.id,
                            title: button.title,
                            ariaLabel: button.getAttribute('aria-label'),
                            innerHTML: button.innerHTML.substring(0, 200),
                            isVisible: button.offsetParent !== null,
                            parentClassName: button.parentElement ? button.parentElement.className : '',
                            hasIcon: button.querySelector('svg, i, .icon') !== null
                        };
                        
                        analysis.buttonsInfo.push(buttonInfo);
                    }
                    
                    // 3. 查找所有可能的复制相关元素
                    const copySelectors = [
                        '[class*="copy"]',
                        '[title*="复制"]',
                        '[aria-label*="复制"]',
                        'svg[class*="copy"]',
                        'i[class*="copy"]',
                        '.cos-icon-copy',
                        '.menu-item'
                    ];
                    
                    for (let selector of copySelectors) {
                        try {
                            const elements = document.querySelectorAll(selector);
                            for (let element of elements) {
                                analysis.copyRelatedElements.push({
                                    selector: selector,
                                    tagName: element.tagName,
                                    className: element.className,
                                    id: element.id,
                                    title: element.title,
                                    innerHTML: element.innerHTML.substring(0, 100),
                                    isVisible: element.offsetParent !== null,
                                    parentClassName: element.parentElement ? element.parentElement.className : ''
                                });
                            }
                        } catch (e) {
                            // 忽略选择器错误
                        }
                    }
                    
                    return analysis;
                }
            """)
            
            print(f"\n📊 页面分析结果:")
            print(f"AI回复区域: {len(page_analysis['aiResponseAreas'])} 个")
            print(f"按钮总数: {len(page_analysis['buttonsInfo'])} 个")
            print(f"复制相关元素: {len(page_analysis['copyRelatedElements'])} 个")
            
            # 详细输出AI回复区域信息
            print(f"\n🤖 AI回复区域详情:")
            for i, area in enumerate(page_analysis['aiResponseAreas'][:5]):  # 只显示前5个
                print(f"区域 {i+1}:")
                print(f"  类名: {area['className'][:100]}")
                print(f"  文本: {area['textContent']}")
                print(f"  按钮数量: {area['buttonCount']}")
                print(f"  父元素类名: {area['parentClassName'][:50]}")
                print("-" * 40)
            
            # 详细输出按钮信息
            print(f"\n🔘 按钮详情 (显示前10个):")
            visible_buttons = [btn for btn in page_analysis['buttonsInfo'] if btn['isVisible']]
            print(f"可见按钮: {len(visible_buttons)} 个")
            
            for i, btn in enumerate(visible_buttons[:10]):
                print(f"按钮 {i+1}:")
                print(f"  类名: {btn['className'][:80]}")
                print(f"  标题: {btn['title']}")
                print(f"  HTML: {btn['innerHTML'][:100]}")
                print(f"  有图标: {btn['hasIcon']}")
                print("-" * 40)
            
            # 详细输出复制相关元素
            print(f"\n📋 复制相关元素详情:")
            visible_copy_elements = [elem for elem in page_analysis['copyRelatedElements'] if elem['isVisible']]
            print(f"可见复制元素: {len(visible_copy_elements)} 个")
            
            for i, elem in enumerate(visible_copy_elements[:10]):
                print(f"复制元素 {i+1}:")
                print(f"  选择器: {elem['selector']}")
                print(f"  标签: {elem['tagName']}")
                print(f"  类名: {elem['className'][:80]}")
                print(f"  HTML: {elem['innerHTML'][:100]}")
                print("-" * 40)
            
            # 尝试悬停激活
            print(f"\n🖱️  尝试悬停激活...")
            try:
                # 查找最可能的AI回复区域
                if page_analysis['aiResponseAreas']:
                    # 选择包含最多内容的区域
                    best_area = max(page_analysis['aiResponseAreas'], 
                                  key=lambda x: len(x['textContent']))
                    
                    print(f"选择最佳区域进行悬停: {best_area['className'][:50]}")
                    
                    # 通过类名查找元素并悬停
                    if best_area['className']:
                        hover_result = await bot.page.evaluate(f"""
                            () => {{
                                const elements = document.querySelectorAll('.{best_area['className'].split()[0]}');
                                if (elements.length > 0) {{
                                    const element = elements[elements.length - 1];
                                    element.dispatchEvent(new MouseEvent('mouseenter', {{bubbles: true}}));
                                    element.dispatchEvent(new MouseEvent('mouseover', {{bubbles: true}}));
                                    return true;
                                }}
                                return false;
                            }}
                        """)
                        
                        if hover_result:
                            print("✅ 悬停事件已触发")
                            await asyncio.sleep(3)  # 等待悬停效果
                            
                            # 重新检查按钮
                            new_buttons = await bot.page.evaluate("""
                                () => {
                                    const buttons = document.querySelectorAll('button');
                                    const visibleButtons = [];
                                    for (let btn of buttons) {
                                        if (btn.offsetParent !== null) {
                                            visibleButtons.push({
                                                className: btn.className,
                                                innerHTML: btn.innerHTML.substring(0, 100),
                                                title: btn.title
                                            });
                                        }
                                    }
                                    return visibleButtons;
                                }
                            """)
                            
                            print(f"悬停后可见按钮: {len(new_buttons)} 个")
                            for i, btn in enumerate(new_buttons[:5]):
                                print(f"  按钮 {i+1}: {btn['className'][:50]} - {btn['title']}")
                        
            except Exception as e:
                print(f"❌ 悬停激活失败: {e}")
            
            # 截图保存
            await bot.page.screenshot(path="page_structure_analysis.png", full_page=True)
            print("📸 页面结构分析截图已保存: page_structure_analysis.png")
            
            # 保存页面HTML
            html_content = await bot.page.content()
            with open("page_structure_analysis.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("💾 页面HTML已保存: page_structure_analysis.html")
            
            print("\n⏸️  浏览器将保持打开60秒，请手动观察页面...")
            await asyncio.sleep(60)
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    try:
        asyncio.run(debug_page_structure())
        print("\n✅ 分析完成")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断分析")
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")


if __name__ == "__main__":
    main()
