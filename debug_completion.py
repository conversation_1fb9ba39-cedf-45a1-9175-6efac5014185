#!/usr/bin/env python3
"""
调试"回答完成"状态检测
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def debug_completion_detection():
    """调试完成状态检测"""
    print("🐛 调试回答完成状态检测...")
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=120000  # 120秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，开始监控状态...")
            
            # 监控状态变化
            start_time = asyncio.get_event_loop().time()
            timeout = 120  # 120秒
            
            last_status = ""
            
            while (asyncio.get_event_loop().time() - start_time) < timeout:
                elapsed = int(asyncio.get_event_loop().time() - start_time)
                print(f"\n⏰ 等待时间: {elapsed}秒")
                
                # 检查完成状态
                completion_status = await bot._check_completion_status()
                print(f"🔍 完成状态检测: {completion_status}")
                
                # 获取页面部分内容来查看状态
                try:
                    # 查找可能包含状态信息的元素
                    status_elements = await bot.page.query_selector_all('*')
                    status_texts = []
                    
                    for element in status_elements[-20:]:  # 检查最后20个元素
                        try:
                            text = await element.inner_text()
                            if text and any(keyword in text for keyword in 
                                          ['DeepSeek', '思考', '回答', '完成', 'thinking']):
                                status_texts.append(text.strip()[:100])
                        except:
                            pass
                    
                    # 去重并显示
                    unique_status = list(set(status_texts))
                    if unique_status:
                        print("📊 发现状态相关文本:")
                        for i, status in enumerate(unique_status[-5:], 1):  # 显示最后5个
                            print(f"  [{i}] {status}")
                            
                            # 检查是否包含完成标识
                            if "回答完成" in status or "思考完成" in status:
                                print(f"  ✅ 发现完成状态: {status}")
                                
                                # 现在尝试获取实际回复
                                print("\n🔍 尝试获取AI回复内容...")
                                response_selectors = [
                                    '.chat-item:last-child',
                                    '.message-item:last-child',
                                    'div[class*="chat"]:last-child',
                                    'div[class*="message"]:last-child'
                                ]
                                
                                for selector in response_selectors:
                                    try:
                                        elements = await bot.page.query_selector_all(selector)
                                        if elements:
                                            for element in elements[-3:]:
                                                element_text = await element.inner_text()
                                                if (element_text and len(element_text.strip()) > 20 and
                                                    bot._is_valid_ai_response(element_text)):
                                                    print(f"✅ 找到有效AI回复:")
                                                    print(f"   选择器: {selector}")
                                                    print(f"   内容: {element_text[:200]}...")
                                                    return element_text
                                    except:
                                        pass
                
                except Exception as e:
                    print(f"❌ 获取状态信息时出错: {e}")
                
                await asyncio.sleep(3)
            
            print("❌ 监控超时")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    try:
        asyncio.run(debug_completion_detection())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试程序异常: {e}")


if __name__ == "__main__":
    main()
