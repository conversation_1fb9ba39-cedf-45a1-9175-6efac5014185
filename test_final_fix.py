#!/usr/bin/env python3
"""
测试最终修复的通用AI回复提取
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_final_fix():
    """测试最终修复"""
    print("🔧 测试最终修复的通用AI回复提取...")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=180000  # 180秒超时
    )
    
    # 测试不同类型的问题
    test_messages = [
        "你好",
        "深圳有哪些著名景点？",
        "请解释一下机器学习的基本概念"
    ]
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            for i, message in enumerate(test_messages, 1):
                print(f"\n📤 测试 {i}: {message}")
                print("-" * 40)
                
                try:
                    # 使用完整的发送方法
                    response = await bot.send_message_stealth(message)
                    
                    print(f"📥 收到回复:")
                    print(f"长度: {len(response)} 字符")
                    print(f"内容: {response[:300]}...")
                    
                    if len(response) > 20:
                        print("✅ 成功提取到完整回复！")
                    else:
                        print("⚠️  回复内容较短，可能不完整")
                    
                    # 等待一下再发送下一个问题
                    if i < len(test_messages):
                        print("⏳ 等待5秒后发送下一个问题...")
                        await asyncio.sleep(5)
                        
                except Exception as e:
                    print(f"❌ 测试 {i} 失败: {e}")
                    continue
            
            print(f"\n🎉 通用提取测试完成！")
            print("现在应该能够处理任何类型的问题，不再依赖特定关键词！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_final_fix())
