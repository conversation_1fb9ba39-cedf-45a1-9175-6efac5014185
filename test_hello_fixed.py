#!/usr/bin/env python3
"""
测试修复后的"你好"回复提取
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_hello_fixed():
    """测试修复后的你好回复"""
    print("👋 测试修复后的'你好'回复提取...")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=180000  # 180秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 测试消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 发送消息
            response = await bot.send_message_stealth(test_message)
            print(f"📥 收到回复: '{response}'")
            
            # 验证回复
            if response and ("你好呀" in response or "你好" in response):
                print("🎉 成功！收到了预期的问候回复！")
            else:
                print(f"⚠️  收到回复但不是预期的问候: '{response}'")
            
            print("\n✅ 测试完成！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_hello_fixed())
