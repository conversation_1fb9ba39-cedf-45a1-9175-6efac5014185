"""
浏览器管理器 - 复用浏览器实例
"""
import asyncio
import random
import logging
from typing import Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page, BrowserContext
from .config import BaiduChatConfig, DEFAULT_CONFIG


class BrowserManager:
    """浏览器管理器 - 单例模式，复用浏览器实例"""
    
    _instance = None
    _playwright = None
    _browser: Optional[Browser] = None
    _context: Optional[BrowserContext] = None
    _current_config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BrowserManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def get_browser_and_context(self, config: BaiduChatConfig):
        """获取浏览器和上下文，如果已存在则复用"""
        
        # 检查是否需要重新创建浏览器（配置变化）
        if (self._browser is None or 
            self._current_config is None or
            self._config_changed(config)):
            
            await self._create_new_browser(config)
        
        # 检查浏览器是否仍然有效
        try:
            if self._browser and not self._browser.is_connected():
                self.logger.warning("浏览器连接已断开，重新创建...")
                await self._create_new_browser(config)
        except:
            self.logger.warning("检查浏览器状态失败，重新创建...")
            await self._create_new_browser(config)
        
        return self._browser, self._context
    
    def _config_changed(self, new_config: BaiduChatConfig) -> bool:
        """检查配置是否发生变化"""
        if self._current_config is None:
            return True
        
        # 检查关键配置是否变化
        return (
            self._current_config.browser_type != new_config.browser_type or
            self._current_config.headless != new_config.headless
        )
    
    async def _create_new_browser(self, config: BaiduChatConfig):
        """创建新的浏览器实例"""
        try:
            # 清理旧的实例
            await self._cleanup()
            
            self.logger.info("创建新的浏览器实例...")
            
            # 启动playwright
            self._playwright = await async_playwright().start()
            
            # 选择浏览器类型
            if config.browser_type == "firefox":
                browser_launcher = self._playwright.firefox
            elif config.browser_type == "webkit":
                browser_launcher = self._playwright.webkit
            else:
                browser_launcher = self._playwright.chromium
            
            # 启动浏览器（隐蔽模式参数）
            self._browser = await browser_launcher.launch(
                headless=config.headless,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                    '--no-first-run',
                    '--disable-default-apps',
                    '--disable-infobars',
                    '--window-size=1280,720'
                ]
            )
            
            # 创建浏览器上下文（模拟真实用户）
            context_options = {
                'viewport': {
                    'width': 1280 + random.randint(-50, 50),
                    'height': 720 + random.randint(-50, 50)
                },
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
                'locale': 'zh-CN',
                'timezone_id': 'Asia/Shanghai',
                'permissions': ['geolocation'],
                'extra_http_headers': {
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1'
                }
            }
            
            self._context = await self._browser.new_context(**context_options)
            self._current_config = config
            
            self.logger.info("浏览器实例创建成功")
            
        except Exception as e:
            self.logger.error(f"创建浏览器失败: {e}")
            await self._cleanup()
            raise
    
    async def create_new_page(self) -> Page:
        """创建新页面"""
        if not self._context:
            raise RuntimeError("浏览器上下文未初始化")
        
        page = await self._context.new_page()
        
        # 注入反检测脚本
        await self._inject_stealth_scripts(page)
        
        return page
    
    async def _inject_stealth_scripts(self, page: Page):
        """注入反检测脚本"""
        stealth_script = """
        // 隐藏webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 修改plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // 修改languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
        
        // 隐藏自动化痕迹
        window.chrome = {
            runtime: {},
        };
        
        // 模拟真实的权限API
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Cypress ? 'denied' : 'granted' }) :
                originalQuery(parameters)
        );
        
        // 覆盖Date对象以避免时区检测
        const originalDate = Date;
        function MockDate(...args) {
            if (args.length === 0) {
                return new originalDate();
            }
            return new originalDate(...args);
        }
        MockDate.now = originalDate.now;
        MockDate.parse = originalDate.parse;
        MockDate.UTC = originalDate.UTC;
        MockDate.prototype = originalDate.prototype;
        window.Date = MockDate;
        """
        
        await page.add_init_script(stealth_script)
    
    async def _cleanup(self):
        """清理资源"""
        try:
            if self._context:
                await self._context.close()
                self._context = None
            
            if self._browser:
                await self._browser.close()
                self._browser = None
            
            if self._playwright:
                await self._playwright.stop()
                self._playwright = None
                
            self._current_config = None
            
        except Exception as e:
            self.logger.error(f"清理浏览器资源时出错: {e}")
    
    async def close_all(self):
        """关闭所有浏览器资源"""
        await self._cleanup()
        BrowserManager._instance = None


# 全局浏览器管理器实例
browser_manager = BrowserManager()
