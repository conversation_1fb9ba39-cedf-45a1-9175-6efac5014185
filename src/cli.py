"""
命令行接口模块
"""
import argparse
import asyncio
import sys
import json
import time
from typing import Optional
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn

from .stealth_bot import StealthBaiduChatBot
from .config import BaiduChatConfig
from .message_handler import MessageHandler


class ChatCLI:
    """聊天命令行接口类"""
    
    def __init__(self):
        self.console = Console()
        self.bot: Optional[StealthBaiduChatBot] = None
        self.config: Optional[BaiduChatConfig] = None

    def _create_progress_with_timer(self):
        """创建带时间显示的进度条"""
        return Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            TimeElapsedColumn(),
            console=self.console
        )

    async def _send_message_with_progress(self, bot, message):
        """发送消息并显示实时进度"""
        start_time = time.time()

        with self._create_progress_with_timer() as progress:
            # 阶段1: 输入消息
            task = progress.add_task("正在输入消息...", total=None)

            # 隐蔽模式分步骤显示
            await bot._input_message_stealth(message)
            progress.update(task, description="正在发送消息...")

            await bot._click_send_button_stealth()
            progress.update(task, description="等待AI回复...")

            # 阶段2: 等待回复
            response = await self._wait_for_response_with_progress(bot, progress, task)

            progress.update(task, description=f"完成 (总用时: {time.time() - start_time:.1f}秒)", completed=True)

        return response



    async def _wait_for_response_with_progress(self, bot, progress, task):
        """等待AI回复并显示进度"""
        start_time = time.time()
        timeout = bot.config.response_timeout / 1000  # 转换为秒

        while time.time() - start_time < timeout:
            elapsed = time.time() - start_time
            progress.update(task, description=f"等待AI回复... ({elapsed:.1f}s)")

            # 检查是否完成
            try:
                completion_status = await bot._check_completion_status()
                if completion_status:
                    progress.update(task, description="AI回复完成，正在提取内容...")
                    # 等待一下确保内容完全加载
                    await asyncio.sleep(2)

                    # 提取回复内容
                    response = await bot._extract_ai_response()
                    return response
            except:
                pass

            await asyncio.sleep(1)  # 每秒更新一次

        raise TimeoutError(f"等待AI回复超时 ({timeout}秒)")
    
    def create_config_from_args(self, args) -> BaiduChatConfig:
        """从命令行参数创建配置"""
        config = BaiduChatConfig()

        # 只有用户明确指定浏览器时才覆盖默认值
        if args.browser is not None:
            config.browser_type = args.browser
        if args.headless:
            config.headless = True
        if args.timeout:
            config.response_timeout = args.timeout * 1000
        if args.debug:
            config.debug = True
        # 浏览器保持打开默认启用，可以通过--close-browser禁用
        if hasattr(args, 'close_browser') and args.close_browser:
            config.keep_browser_open = False

        return config
    
    async def single_message_mode(self, message: str, config: BaiduChatConfig):
        """单次消息模式"""
        try:
            # 初始化阶段使用进度条
            with self._create_progress_with_timer() as progress:
                task = progress.add_task("正在初始化浏览器...", total=None)

                # 初始化隐蔽模式机器人
                bot = StealthBaiduChatBot(config)
                await bot.__aenter__()
                progress.update(task, description="初始化完成", completed=True)

            # 发送消息阶段
            response = await self._send_message_with_progress(bot, message)

            # 显示结果
            self._display_response(message, response)

            # 根据配置决定是否关闭浏览器
            if not config.keep_browser_open:
                await bot.__aexit__(None, None, None)
            else:
                # 保持浏览器打开，只重置状态
                bot.is_initialized = False
                self.console.print("[dim]浏览器保持打开状态，可复用于下次查询[/dim]")

        except Exception as e:
            self.console.print(f"[red]错误: {e}[/red]")
            sys.exit(1)
    
    async def interactive_mode(self, config: BaiduChatConfig):
        """交互式聊天模式"""
        self.console.print(Panel.fit(
            "[bold blue]百度AI聊天机器人[/bold blue]\n"
            "输入 'quit' 或 'exit' 退出\n"
            "输入 'clear' 清空对话历史\n"
            "输入 'history' 查看对话历史",
            title="欢迎使用"
        ))

        try:
            # 初始化阶段使用进度条
            with self._create_progress_with_timer() as progress:
                task = progress.add_task("正在初始化浏览器...", total=None)

                # 初始化隐蔽模式机器人
                bot = StealthBaiduChatBot(config)
                await bot.__aenter__()
                progress.update(task, description="初始化完成", completed=True)

            # 初始化完成后进入交互循环
            try:
                while True:
                    try:
                        # 获取用户输入
                        user_input = Prompt.ask("\n[bold green]你[/bold green]")

                        if not user_input.strip():
                            continue

                        # 处理特殊命令
                        if user_input.lower() in ['quit', 'exit', 'q']:
                            self.console.print("[yellow]再见![/yellow]")
                            break
                        elif user_input.lower() == 'clear':
                            bot.clear_conversation_history()
                            self.console.print("[yellow]对话历史已清空[/yellow]")
                            continue
                        elif user_input.lower() == 'history':
                            self._display_history(bot.get_conversation_history())
                            continue

                        # 发送消息
                        response = await self._send_message_with_progress(bot, user_input)

                        # 显示回复
                        self._display_ai_response(response)

                    except KeyboardInterrupt:
                        self.console.print("\n[yellow]用户中断，正在退出...[/yellow]")
                        break
                    except Exception as e:
                        self.console.print(f"[red]发送消息时出错: {e}[/red]")
                        continue
            finally:
                # 确保机器人正确关闭
                await bot.__aexit__(None, None, None)

        except Exception as e:
            self.console.print(f"[red]初始化失败: {e}[/red]")
            sys.exit(1)
    
    def _display_response(self, user_message: str, ai_response: str):
        """显示单次对话结果"""
        # 显示用户消息
        self.console.print(Panel(
            user_message,
            title="[bold green]你的消息[/bold green]",
            border_style="green"
        ))
        
        # 显示AI回复
        self.console.print(Panel(
            ai_response,
            title="[bold blue]AI回复[/bold blue]",
            border_style="blue"
        ))
    
    def _display_ai_response(self, response: str):
        """显示AI回复"""
        self.console.print(Panel(
            response,
            title="[bold blue]AI[/bold blue]",
            border_style="blue"
        ))
    
    def _display_history(self, history: list):
        """显示对话历史"""
        if not history:
            self.console.print("[yellow]暂无对话历史[/yellow]")
            return
        
        self.console.print(Panel.fit(
            f"共有 {len(history)} 条对话记录",
            title="对话历史"
        ))
        
        for i, conversation in enumerate(history, 1):
            timestamp = conversation.get('timestamp', '未知时间')
            user_msg = conversation.get('user_message', '')
            ai_msg = conversation.get('ai_response', '')
            
            self.console.print(f"\n[bold]第 {i} 条对话[/bold] ({timestamp[:19]})")
            self.console.print(f"[green]你:[/green] {user_msg}")
            self.console.print(f"[blue]AI:[/blue] {ai_msg}")
    
    def parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(
            description="百度AI聊天机器人命令行工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  # 单次查询（隐蔽模式）
  python main.py --message "你好，请介绍一下人工智能"

  # 交互式聊天（隐蔽模式）
  python main.py --interactive

  # 使用Firefox浏览器
  python main.py --browser firefox --interactive

  # 无头模式运行
  python main.py --headless --message "Hello"

  # 关闭浏览器（默认保持打开）
  python main.py --message "测试" --close-browser
            """
        )
        
        # 消息相关参数
        parser.add_argument(
            '-m', '--message',
            type=str,
            help='要发送的消息（单次查询模式）'
        )
        
        parser.add_argument(
            '-i', '--interactive',
            action='store_true',
            help='启动交互式聊天模式'
        )
        
        # 浏览器配置
        parser.add_argument(
            '--browser',
            choices=['chromium', 'firefox', 'webkit'],
            default=None,  # 不设置默认值，使用配置文件中的默认值
            help='选择浏览器类型 (默认: webkit)'
        )
        
        parser.add_argument(
            '--headless',
            action='store_true',
            help='无头模式运行（不显示浏览器窗口）'
        )

        parser.add_argument(
            '--close-browser',
            action='store_true',
            help='任务完成后关闭浏览器页面（默认保持打开以便复用）'
        )
        
        # 超时配置
        parser.add_argument(
            '--timeout',
            type=int,
            default=180,
            help='AI回复超时时间（秒，默认180秒）'
        )
        
        # 调试选项
        parser.add_argument(
            '--debug',
            action='store_true',
            help='启用调试模式'
        )


        
        parser.add_argument(
            '--version',
            action='version',
            version='百度AI聊天机器人 v2.0.0 (隐蔽模式专版)'
        )
        
        return parser.parse_args()
    
    async def run(self):
        """运行CLI应用"""
        args = self.parse_arguments()
        
        # 验证参数
        if not args.message and not args.interactive:
            self.console.print("[red]错误: 请指定 --message 或 --interactive 参数[/red]")
            sys.exit(1)
        
        if args.message and args.interactive:
            self.console.print("[red]错误: --message 和 --interactive 不能同时使用[/red]")
            sys.exit(1)
        
        # 创建配置
        config = self.create_config_from_args(args)
        
        # 运行相应模式
        if args.message:
            await self.single_message_mode(args.message, config)
        else:
            await self.interactive_mode(config)


def main():
    """主函数"""
    cli = ChatCLI()
    try:
        asyncio.run(cli.run())
    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(0)
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
