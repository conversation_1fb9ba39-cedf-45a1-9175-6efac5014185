"""
隐蔽模式聊天机器人 - 绕过反爬虫检测
"""
import asyncio
import random
import logging
from typing import Optional
from playwright.async_api import Page
from .config import BaiduChatConfig, DEFAULT_CONFIG
from .message_handler import MessageHandler
from .browser_manager import browser_manager


class StealthBaiduChatBot:
    """隐蔽模式百度聊天机器人"""
    
    def __init__(self, config: Optional[BaiduChatConfig] = None):
        self.config = config or DEFAULT_CONFIG
        self.message_handler = MessageHandler()
        self.page: Optional[Page] = None
        self.is_initialized = False

        # 设置日志
        self.logger = logging.getLogger(__name__)
        if self.config.debug:
            logging.basicConfig(level=logging.DEBUG)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def initialize(self):
        """初始化浏览器和页面（隐蔽模式）"""
        try:
            self.logger.info("正在初始化聊天页面...")

            # 获取或复用浏览器实例
            browser, context = await browser_manager.get_browser_and_context(self.config)

            # 创建新页面
            self.page = await browser_manager.create_new_page()

            # 设置超时
            self.page.set_default_timeout(self.config.element_timeout)

            # 导航到百度聊天页面
            await self._navigate_to_chat_page_stealth()

            self.is_initialized = True
            self.logger.info("聊天页面初始化完成")

        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            await self.close()
            raise
    

    
    async def _navigate_to_chat_page_stealth(self):
        """隐蔽模式导航到聊天页面"""
        self.logger.info(f"正在访问 {self.config.base_url}")

        try:
            # 直接访问聊天页面（隐蔽模式）
            await self.page.goto(
                self.config.base_url,
                wait_until='domcontentloaded',
                timeout=self.config.page_load_timeout
            )

            # 等待页面基本加载完成
            try:
                await self.page.wait_for_load_state('networkidle', timeout=30000)  # 增加到30秒
            except:
                self.logger.warning("页面networkidle等待超时，继续执行")
                pass

            # 等待页面稳定
            await self._random_delay(2, 4)

            # 模拟用户浏览行为
            await self._simulate_browsing_behavior()

            # 等待聊天界面元素出现
            await self._wait_for_chat_interface_stealth()

            self.logger.info("成功访问聊天页面")

        except Exception as e:
            self.logger.error(f"访问聊天页面失败: {e}")
            raise
    
    async def _simulate_mouse_movement(self):
        """模拟真实的鼠标移动"""
        for _ in range(random.randint(2, 5)):
            x = random.randint(100, 1000)
            y = random.randint(100, 600)
            await self.page.mouse.move(x, y)
            await self._random_delay(0.1, 0.3)
    
    async def _simulate_browsing_behavior(self):
        """模拟真实的浏览行为"""
        # 简单的鼠标移动
        await self._simulate_mouse_movement()

        # 随机滚动（如果页面支持）
        try:
            for _ in range(random.randint(1, 2)):
                await self.page.mouse.wheel(0, random.randint(50, 150))
                await self._random_delay(0.5, 1.0)
        except:
            # 如果滚动失败，忽略
            pass

        # 简单等待
        await self._random_delay(1, 2)
    
    async def _random_delay(self, min_seconds: float, max_seconds: float):
        """随机延迟"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def _wait_for_chat_interface_stealth(self):
        """等待聊天界面加载完成（隐蔽模式）"""
        input_selectors = [
            '#chat-input-box',
            '[contenteditable="true"]',
            '.cs-pc-input-custom'
        ]
        
        for selector in input_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=30000)  # 增加到30秒
                self.logger.debug(f"找到输入框: {selector}")
                return
            except:
                continue
        
        # 如果都没找到，等待一段时间后再试
        await self._random_delay(3, 5)
        self.logger.warning("未能立即找到输入框，将在发送消息时重试")
    
    async def send_message_stealth(self, message: str) -> str:
        """隐蔽模式发送消息"""
        if not self.is_initialized:
            raise RuntimeError("聊天机器人未初始化，请先调用 initialize()")
        
        # 验证消息
        is_valid, error_msg = self.message_handler.validate_message(message)
        if not is_valid:
            raise ValueError(f"消息验证失败: {error_msg}")
        
        # 清理消息
        clean_message = self.message_handler.clean_input_message(message)
        
        try:
            self.logger.info(f"正在发送消息: {clean_message[:50]}...")
            
            # 检查是否有验证码
            if await self._check_captcha():
                self.logger.warning("检测到验证码，尝试处理...")
                await self._handle_captcha()
            
            # 模拟真实用户行为发送消息
            await self._input_message_stealth(clean_message)
            await self._click_send_button_stealth()
            
            # 等待并获取回复
            response = await self._wait_for_response_stealth()
            
            # 处理回复
            clean_response = self.message_handler.extract_response_from_element(response)
            
            # 添加到历史记录
            self.message_handler.add_to_history(clean_message, clean_response)
            
            self.logger.info(f"收到回复: {clean_response[:50]}...")
            
            return clean_response
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            raise
    
    async def _check_captcha(self) -> bool:
        """检查是否有验证码"""
        captcha_selectors = [
            '.captcha',
            '.verify',
            '.security',
            '[class*="captcha"]',
            '[class*="verify"]',
            'iframe[src*="captcha"]'
        ]
        
        for selector in captcha_selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    return True
            except:
                continue
        
        return False
    
    async def _handle_captcha(self):
        """处理验证码（简单策略）"""
        self.logger.info("检测到验证码，等待用户手动处理...")
        
        # 等待验证码消失或用户手动处理
        for _ in range(30):  # 最多等待30秒
            await asyncio.sleep(1)
            if not await self._check_captcha():
                self.logger.info("验证码已处理完成")
                return
        
        raise RuntimeError("验证码处理超时")
    
    async def _input_message_stealth(self, message: str):
        """隐蔽模式输入消息"""
        input_selectors = [
            '#chat-input-box',
            '[contenteditable="true"]',
            '.cs-pc-input-custom'
        ]
        
        input_element = None
        for selector in input_selectors:
            try:
                input_element = await self.page.wait_for_selector(selector, timeout=30000)  # 增加到30秒
                if input_element:
                    break
            except:
                continue
        
        if not input_element:
            raise RuntimeError("无法找到输入框")
        
        # 模拟真实用户点击
        await input_element.click()
        await self._random_delay(0.5, 1.0)
        
        # 清空输入框
        await self.page.keyboard.press('Control+a')
        await self.page.keyboard.press('Delete')
        await self._random_delay(0.2, 0.5)
        
        # 模拟真实打字（随机延迟）
        for char in message:
            await self.page.keyboard.type(char)
            # 随机打字延迟
            delay = random.uniform(0.05, 0.2)
            await asyncio.sleep(delay)
        
        self.logger.debug("消息输入完成")
    
    async def _click_send_button_stealth(self):
        """隐蔽模式点击发送按钮"""
        # 模拟真实用户的发送行为
        await self._random_delay(0.5, 1.5)
        
        # 尝试Enter键发送
        await self.page.keyboard.press('Enter')
        await self._random_delay(1, 2)
        
        self.logger.debug("使用Enter键发送消息")
    
    async def _wait_for_response_stealth(self) -> str:
        """隐蔽模式等待AI回复"""
        # 状态检测选择器 - 检测"回答完成"状态
        status_selectors = [
            # 查找包含"回答完成"的元素
            '*:contains("回答完成")',
            '*:contains("思考完成")',
            # 通用状态选择器
            '.status',
            '.completion-status',
            '.response-status'
        ]

        # 回复内容选择器 - 基于调试结果优化
        response_selectors = [
            # 最精确的选择器 - 基于调试发现的有效元素
            # 查找所有div元素，然后通过内容验证来筛选
            'div',

            # 百度AI特定的容器选择器
            '.ai-entry-box_11wt5_195',
            '.ai-entry',
            '.cs-answer-container',
            '.answer-container',
            '.chat-search-answer-generate-item',

            # 基于调试结果的准确选择器
            '.chat-item:last-child .chat-content',
            '.chat-item:last-child',
            '.message-item:last-child .content',
            '.message-item:last-child',

            # 百度AI特定选择器
            '.response-text',
            '.ai-message',
            '.bot-message',
            '.chat-answer',
            '.answer-content',

            # 通用选择器
            '.chat-message:last-child',
            '.message:last-child',
            '.response:last-child',
            '.reply:last-child',

            # 更广泛的选择器
            'div[class*="chat"]:last-child',
            'div[class*="message"]:last-child',
            'div[class*="response"]:last-child',

            # 最后才尝试answer相关选择器
            '[class*="answer"]'
        ]
        
        start_time = asyncio.get_event_loop().time()
        timeout = self.config.response_timeout / 1000

        thinking_detected = False
        completion_detected = False

        self.logger.debug("开始等待AI回复...")

        # 第一阶段：等待"回答完成"状态
        while (asyncio.get_event_loop().time() - start_time) < timeout:
            try:
                # 检查是否有"回答完成"状态
                completion_status = await self._check_completion_status()
                if completion_status:
                    self.logger.debug("✅ 检测到回答完成状态，开始提取回复内容...")
                    completion_detected = True
                    # 等待一下确保内容完全加载
                    await asyncio.sleep(3)
                    break

                # 检查是否在思考中
                page_content = await self.page.content()
                if ("思考中" in page_content or "thinking" in page_content.lower() or
                    "DeepSeek-R1满血版 思考中" in page_content):
                    if not thinking_detected:
                        self.logger.debug("检测到AI正在思考...")
                        thinking_detected = True

                await asyncio.sleep(2)  # 每2秒检查一次

            except Exception as e:
                self.logger.debug(f"等待完成状态时出错: {e}")
                await asyncio.sleep(2)

        # 如果没有检测到完成状态，直接超时
        if not completion_detected:
            self.logger.debug("❌ 未检测到回答完成状态，等待超时")
            raise TimeoutError("等待AI回复超时 - 未检测到回答完成状态")

        # 第二阶段：提取AI回复内容
        self.logger.debug("开始提取AI回复内容...")
        extraction_timeout = 10  # 给提取过程10秒时间
        extraction_start = asyncio.get_event_loop().time()

        while (asyncio.get_event_loop().time() - extraction_start) < extraction_timeout:
            try:
                # 使用通用方法提取AI回复
                ai_response = await self.page.evaluate("""
                    () => {
                        // 查找"回答完成"后的内容
                        const allElements = document.querySelectorAll('*');
                        let completionElement = null;

                        for (let element of allElements) {
                            const text = element.innerText || '';
                            if (text.includes('回答完成')) {
                                completionElement = element;
                                break;
                            }
                        }

                        if (completionElement) {
                            let current = completionElement;

                            while (current && current.parentElement) {
                                const parent = current.parentElement;
                                const siblings = Array.from(parent.children);
                                const currentIndex = siblings.indexOf(current);

                                for (let i = currentIndex + 1; i < siblings.length; i++) {
                                    const sibling = siblings[i];
                                    const siblingText = sibling.innerText || '';

                                    // 通用条件：长度合适且不是界面元素
                                    if (siblingText.length > 10 && siblingText.length < 5000 &&
                                        !siblingText.includes('DeepSeek-R1满血版') &&
                                        !siblingText.includes('回答完成') &&
                                        !siblingText.includes('思考完成') &&
                                        !siblingText.includes('思考中') &&
                                        !siblingText.includes('登录') &&
                                        !siblingText.includes('注册') &&
                                        !siblingText.includes('百度首页') &&
                                        !siblingText.includes('发送') &&
                                        !siblingText.includes('提问') &&
                                        !siblingText.includes('搜索')) {

                                        console.log('找到回答完成后的内容:', siblingText.substring(0, 100));
                                        return siblingText.trim();
                                    }
                                }

                                current = parent;
                            }
                        }

                        return null;
                    }
                """)

                if ai_response and len(ai_response.strip()) > 10:
                    self.logger.debug("✅ 成功提取AI回复内容")
                    return ai_response.strip()

                await asyncio.sleep(1)

            except Exception as e:
                self.logger.debug(f"提取回复内容时出错: {e}")
                await asyncio.sleep(1)

        # 如果提取阶段也超时了
        self.logger.debug("❌ 提取AI回复内容超时")
        raise TimeoutError("等待AI回复超时 - 无法提取回复内容")

    async def _check_completion_status(self) -> bool:
        """检查是否有回答完成状态"""
        try:
            # 检查页面中是否包含"回答完成"文本
            completion_indicators = [
                "回答完成",
                "思考完成",
                "DeepSeek-R1满血版 回答完成",
                "DeepSeek-R1满血版 思考完成"
            ]

            # 获取页面所有文本内容
            page_content = await self.page.content()

            for indicator in completion_indicators:
                if indicator in page_content:
                    return True

            # 也可以尝试通过选择器查找
            completion_selectors = [
                '*:has-text("回答完成")',
                '*:has-text("思考完成")',
                '*:has-text("DeepSeek-R1满血版")'
            ]

            for selector in completion_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    if elements:
                        for element in elements:
                            text = await element.inner_text()
                            if any(indicator in text for indicator in completion_indicators):
                                return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"检查完成状态时出错: {e}")
            return False

    def _is_valid_ai_response(self, text: str) -> bool:
        """判断文本是否是有效的AI回复"""
        if not text or len(text.strip()) < 3:
            return False

        text = text.strip()

        # 排除明显不是AI回复的内容
        invalid_patterns = [
            # 界面元素
            '提问', '发送', '搜索', '登录', '注册', '思考中', 'thinking',
            '开启新对话', '知识库', '智能体', '反馈', '快捷入口',
            '深度搜索', '快速回答', '深度思考',

            # 推荐问题的特征（通常是问号结尾的短句）
            '你如何获取知识的？', '你可以帮我写论文吗？', '你是怎么被研发出来的？',

            # 其他界面文本
            '百度首页', '网页', '图片', '资讯', '视频', '笔记', '地图', '贴吧', '文库',
            '更多', '问问智能体',

            # 思考状态文本
            'DeepSeek-R1满血版 思考中', '嗯，用户发来', '回答完成', '思考完成'
        ]

        # 检查是否包含无效模式
        for pattern in invalid_patterns:
            if pattern in text:
                return False

        # 检查是否是推荐问题列表（多个问号）
        question_count = text.count('？') + text.count('?')
        if question_count >= 3 and len(text) < 300:
            return False

        # 检查是否包含AI回复的特征（更宽松的条件）
        ai_response_indicators = [
            'DeepSeek', 'AI助手', '我是', '我的使命', '帮助你',
            '很高兴', '可以为你', '我可以', '让我', '根据', '关于',
            '这是', '这个', '可以', '建议', '推荐', '认为', '觉得',
            '首先', '其次', '最后', '总的来说', '总结', '综上',
            '👋', '✨', '🌟', '😊', '🤔', '💡'  # 常见的AI回复表情符号
        ]

        # 如果包含AI回复特征，且长度合理，认为是有效回复
        for indicator in ai_response_indicators:
            if indicator in text and len(text) >= 3:
                return True

        # 如果文本较长且不包含太多问号，也可能是有效回复
        if len(text) > 30 and question_count < 2:
            return True

        # 如果文本很长，很可能是有效的AI回复
        if len(text) > 100:
            return True

        return False

    def _is_valid_ai_response_relaxed(self, text: str) -> bool:
        """更宽松的AI回复验证"""
        if not text or len(text.strip()) < 5:
            return False

        text = text.strip()

        # 只排除最明显的无效内容
        strict_invalid_patterns = [
            'DeepSeek-R1满血版',
            '思考中',
            '回答完成',
            '思考完成',
            '登录',
            '注册',
            '百度首页',
            '开启新对话'
        ]

        # 检查是否包含严格的无效模式
        for pattern in strict_invalid_patterns:
            if pattern in text:
                return False

        # 如果文本长度合理，就认为是有效的
        if len(text) >= 5:
            return True

        return False

    async def _extract_ai_response(self) -> str:
        """提取AI回复内容"""
        try:
            # 策略1: 查找"回答完成"下面的输出框内容
            ai_response = await self.page.evaluate("""
                () => {
                    // 首先查找包含"回答完成"的元素
                    const allElements = document.querySelectorAll('*');
                    let completionElement = null;

                    for (let element of allElements) {
                        const text = element.innerText || '';
                        if (text.includes('回答完成')) {
                            completionElement = element;
                            break;
                        }
                    }

                    if (completionElement) {
                        // 查找"回答完成"元素后面的兄弟元素或父元素的后续元素
                        let current = completionElement;

                        // 向上查找父元素，然后查找后续的兄弟元素
                        while (current && current.parentElement) {
                            const parent = current.parentElement;
                            const siblings = Array.from(parent.children);
                            const currentIndex = siblings.indexOf(current);

                            // 查找当前元素后面的兄弟元素
                            for (let i = currentIndex + 1; i < siblings.length; i++) {
                                const sibling = siblings[i];
                                const siblingText = sibling.innerText || '';

                                // 查找包含AI回复特征的内容
                                if (siblingText.length > 10 &&
                                    (
                                     siblingText.includes('很高兴') ||
                                     siblingText.includes('👋') ||
                                     siblingText.includes('�') ||
                                     siblingText.includes('帮你') ||
                                     siblingText.includes('解答') ||
                                     siblingText.includes('建议'))) {
                                    console.log('找到回答完成后的AI回复:', siblingText.substring(0, 100));
                                    return siblingText.trim();
                                }
                            }

                            current = parent;
                        }
                    }

                    return null;
                }
            """)

            if ai_response and len(ai_response.strip()) > 10:
                cleaned_response = ai_response.strip()
                self.logger.debug(f"策略1成功提取: {cleaned_response[:100]}...")
                return cleaned_response

            # 策略2: 查找包含完整AI回复特征的长文本
            ai_response = await self.page.evaluate("""
                () => {
                    const allElements = document.querySelectorAll('div, p, span');
                    const candidates = [];

                    for (let element of allElements) {
                        const text = element.innerText || '';

                        // 查找包含完整AI回复特征的较长文本
                        if (text.length > 20 && text.length < 2000 &&
                            ((text.includes('很高兴') && text.includes('👋')) ||
                            (text.includes('很高兴见到你') && text.includes('帮你')) ||
                            (text.includes('解答问题') && text.includes('提供建议')))) {

                            // 排除明显的界面元素
                            if (!text.includes('DeepSeek-R1满血版') &&
                                !text.includes('回答完成') &&
                                !text.includes('思考完成') &&
                                !text.includes('登录') &&
                                !text.includes('注册') &&
                                !text.includes('百度首页')) {

                                candidates.push({
                                    text: text.trim(),
                                    length: text.length
                                });
                            }
                        }
                    }

                    // 返回最长的候选项（最完整的AI回复）
                    if (candidates.length > 0) {
                        candidates.sort((a, b) => b.length - a.length);
                        console.log('策略2找到候选:', candidates[0].text.substring(0, 100));
                        return candidates[0].text;
                    }

                    return null;
                }
            """)

            if ai_response and len(ai_response.strip()) > 10:
                cleaned_response = ai_response.strip()
                self.logger.debug(f"策略2成功提取: {cleaned_response[:100]}...")
                return cleaned_response

            # 策略3: 查找任何包含AI回复关键词的较长文本
            ai_response = await self.page.evaluate("""
                () => {
                    const allElements = document.querySelectorAll('*');
                    const candidates = [];

                    for (let element of allElements) {
                        const text = element.innerText || '';

                        // 查找包含AI回复关键词的文本
                        if (text.length > 15 && text.length < 1000 &&
                            (text.includes('很高兴见到你') ||
                             text.includes('有什么我可以帮') ||
                             text.includes('解答问题') ||
                             text.includes('提供建议') ||
                             (text.includes('👋') && text.includes('😊')))) {

                            // 排除界面元素
                            if (!text.includes('DeepSeek-R1满血版') &&
                                !text.includes('思考中') &&
                                !text.includes('登录') &&
                                !text.includes('注册')) {

                                candidates.push(text.trim());
                            }
                        }
                    }

                    // 返回最长的候选
                    if (candidates.length > 0) {
                        candidates.sort((a, b) => b.length - a.length);
                        console.log('策略3找到候选:', candidates[0].substring(0, 100));
                        return candidates[0];
                    }

                    return null;
                }
            """)

            if ai_response and len(ai_response.strip()) > 10:
                cleaned_response = ai_response.strip()
                self.logger.debug(f"策略3成功提取: {cleaned_response[:100]}...")
                return cleaned_response

            raise RuntimeError("无法提取有效的AI回复内容")

        except Exception as e:
            self.logger.error(f"提取AI回复失败: {e}")
            raise

    async def close(self):
        """根据配置决定是否关闭页面"""
        try:
            if self.config.keep_browser_open:
                # 保持浏览器和页面打开，只重置状态
                self.is_initialized = False
                self.logger.info("保持浏览器和页面打开，任务完成")
            else:
                # 关闭页面（保持浏览器运行）
                if self.page:
                    await self.page.close()
                    self.page = None

                self.is_initialized = False
                self.logger.info("聊天页面已关闭")

        except Exception as e:
            self.logger.error(f"关闭页面时出错: {e}")

    @staticmethod
    async def close_browser():
        """关闭整个浏览器（静态方法）"""
        await browser_manager.close_all()
    
    def get_conversation_history(self):
        """获取对话历史"""
        return self.message_handler.get_conversation_history()
    
    def clear_conversation_history(self):
        """清空对话历史"""
        self.message_handler.clear_history()
