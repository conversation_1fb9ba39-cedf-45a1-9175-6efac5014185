#!/usr/bin/env python3
"""
测试复制按钮方案的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_copy_button_method():
    """测试复制按钮获取AI回复的方法"""
    print("🧪 测试复制按钮方案")
    print("=" * 50)
    
    # 配置
    config = BaiduChatConfig(
        base_url="https://chat.baidu.com/",
        response_timeout=180000,  # 3分钟超时
        keep_browser_open=True,   # 保持浏览器打开便于观察
        debug=True
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 测试消息
            test_message = "请简单介绍一下你自己"
            print(f"📤 发送测试消息: {test_message}")
            
            # 使用新的复制按钮方法
            try:
                response = await bot.send_message_stealth(test_message)
                
                if response and response.strip():
                    print(f"\n🎉 成功获取AI回复:")
                    print("=" * 60)
                    print(f"用户: {test_message}")
                    print(f"AI: {response}")
                    print("=" * 60)
                    print(f"📊 回复长度: {len(response)} 字符")
                else:
                    print("❌ 获取到空回复")
                    
            except Exception as e:
                print(f"❌ 发送消息失败: {e}")
                
                # 截图保存当前状态
                await bot.page.screenshot(path="copy_button_error.png", full_page=True)
                print("📸 错误状态截图已保存: copy_button_error.png")
            
            # 保持浏览器打开一段时间便于观察
            print("\n⏸️  浏览器将保持打开30秒，请观察页面...")
            await asyncio.sleep(30)
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def debug_copy_buttons():
    """调试页面上的复制按钮"""
    print("🔍 调试复制按钮")
    print("=" * 50)
    
    config = BaiduChatConfig(
        base_url="https://chat.baidu.com/",
        keep_browser_open=True,
        debug=True
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送一个简单消息
            test_message = "你好"
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复...")
            
            # 等待回答完成
            await bot._wait_for_completion_status()
            print("✅ 回答完成，开始查找复制按钮...")
            
            # 查找所有可能的复制按钮
            copy_button_selectors = [
                '.cos-icon-copy',
                '.menu-item_19lnq_32',
                'span:has(.cos-icon-copy)',
                '[data-click-log*="copy"]',
                'button[title*="复制"]',
                'button[aria-label*="复制"]',
                '.copy-button',
                '.copy-btn',
                '[class*="copy"]',
                '.icon-copy',
                '.fa-copy',
                '.copy-icon'
            ]
            
            found_buttons = []
            for selector in copy_button_selectors:
                try:
                    elements = await bot.page.query_selector_all(selector)
                    if elements:
                        print(f"✅ 找到复制按钮: {selector} ({len(elements)}个)")
                        found_buttons.extend([(selector, elem) for elem in elements])
                except Exception as e:
                    print(f"❌ 查找 {selector} 失败: {e}")
            
            if found_buttons:
                print(f"\n📊 总共找到 {len(found_buttons)} 个复制按钮")
                
                # 尝试点击最后一个复制按钮
                try:
                    last_selector, last_button = found_buttons[-1]
                    print(f"🖱️  尝试点击最后一个复制按钮: {last_selector}")
                    
                    await last_button.scroll_into_view_if_needed()
                    await asyncio.sleep(1)
                    await last_button.click()
                    print("✅ 复制按钮点击成功")
                    
                    # 等待复制完成
                    await asyncio.sleep(2)
                    
                    # 读取剪贴板
                    clipboard_content = await bot.page.evaluate("""
                        async () => {
                            try {
                                return await navigator.clipboard.readText();
                            } catch (e) {
                                return 'Error: ' + e.message;
                            }
                        }
                    """)
                    
                    if clipboard_content:
                        print(f"📋 剪贴板内容: {clipboard_content[:200]}...")
                    else:
                        print("❌ 剪贴板为空")
                        
                except Exception as e:
                    print(f"❌ 点击复制按钮失败: {e}")
            else:
                print("❌ 未找到任何复制按钮")
            
            # 截图保存
            await bot.page.screenshot(path="debug_copy_buttons.png", full_page=True)
            print("📸 调试截图已保存: debug_copy_buttons.png")
            
            # 保持浏览器打开
            print("\n⏸️  浏览器将保持打开30秒...")
            await asyncio.sleep(30)
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🧪 复制按钮方案测试工具")
    print("选择测试模式:")
    print("1. 测试完整的复制按钮方案")
    print("2. 调试复制按钮")
    
    choice = input("请输入选择 (1-2): ").strip()
    
    try:
        if choice == "1":
            asyncio.run(test_copy_button_method())
        elif choice == "2":
            asyncio.run(debug_copy_buttons())
        else:
            print("❌ 无效选择")
            return
            
        print("\n✅ 测试完成")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
