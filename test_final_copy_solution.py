#!/usr/bin/env python3
"""
最终的复制按钮解决方案测试
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_final_copy_solution():
    """测试最终的复制按钮解决方案"""
    print("🎯 测试最终复制按钮解决方案")
    print("=" * 50)
    
    config = BaiduChatConfig(
        base_url="https://chat.baidu.com/",
        response_timeout=180000,
        keep_browser_open=True,
        debug=True
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 测试完整的发送消息流程
            test_message = "请简单介绍一下你自己"
            print(f"📤 发送消息: {test_message}")
            
            try:
                # 使用新的复制按钮方法
                response = await bot.send_message_stealth(test_message)
                
                if response and response.strip():
                    print(f"\n🎉 成功获取AI回复:")
                    print("=" * 60)
                    print(f"用户: {test_message}")
                    print(f"AI: {response}")
                    print("=" * 60)
                    print(f"📊 回复长度: {len(response)} 字符")
                    
                    # 验证回复质量
                    if len(response) > 50:
                        print("✅ 回复内容充实，复制按钮方案成功！")
                    else:
                        print("⚠️  回复内容较短，可能需要进一步优化")
                else:
                    print("❌ 获取到空回复")
                    
            except Exception as e:
                print(f"❌ 发送消息失败: {e}")
                
                # 截图保存错误状态
                await bot.page.screenshot(path="final_copy_error.png", full_page=True)
                print("📸 错误状态截图已保存: final_copy_error.png")
            
            # 保持浏览器打开便于观察
            print("\n⏸️  浏览器将保持打开30秒，请观察结果...")
            await asyncio.sleep(30)
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_copy_button_vs_dom_parsing():
    """对比测试复制按钮方法和DOM解析方法"""
    print("🔄 对比测试：复制按钮 vs DOM解析")
    print("=" * 50)
    
    config = BaiduChatConfig(
        base_url="https://chat.baidu.com/",
        response_timeout=180000,
        keep_browser_open=True,
        debug=True
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复...")
            await bot._wait_for_completion_status()
            print("✅ 回答完成")
            
            # 方法1: 尝试复制按钮方法
            print("\n📋 方法1: 复制按钮方法")
            copy_result = None
            try:
                copy_result = await bot._get_response_via_copy_button()
                if copy_result:
                    print(f"✅ 复制按钮成功: {copy_result[:100]}...")
                else:
                    print("❌ 复制按钮失败")
            except Exception as e:
                print(f"❌ 复制按钮方法出错: {e}")
            
            # 方法2: DOM解析方法
            print("\n🔍 方法2: DOM解析方法")
            dom_result = None
            try:
                dom_result = await bot._wait_for_response_stealth()
                if dom_result:
                    print(f"✅ DOM解析成功: {dom_result[:100]}...")
                else:
                    print("❌ DOM解析失败")
            except Exception as e:
                print(f"❌ DOM解析方法出错: {e}")
            
            # 对比结果
            print("\n📊 结果对比:")
            if copy_result and dom_result:
                print(f"复制按钮长度: {len(copy_result)} 字符")
                print(f"DOM解析长度: {len(dom_result)} 字符")
                
                if copy_result == dom_result:
                    print("✅ 两种方法结果完全一致")
                elif copy_result in dom_result or dom_result in copy_result:
                    print("⚠️  两种方法结果部分重叠")
                else:
                    print("❌ 两种方法结果不同")
                    print(f"复制按钮结果: {copy_result[:200]}...")
                    print(f"DOM解析结果: {dom_result[:200]}...")
            elif copy_result:
                print("✅ 只有复制按钮方法成功")
            elif dom_result:
                print("✅ 只有DOM解析方法成功")
            else:
                print("❌ 两种方法都失败")
            
            # 截图保存
            await bot.page.screenshot(path="comparison_test.png", full_page=True)
            print("📸 对比测试截图已保存: comparison_test.png")
            
            print("\n⏸️  浏览器将保持打开30秒...")
            await asyncio.sleep(30)
                
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🧪 复制按钮最终解决方案测试")
    print("选择测试模式:")
    print("1. 测试最终复制按钮解决方案")
    print("2. 对比测试复制按钮 vs DOM解析")
    
    choice = input("请输入选择 (1-2): ").strip()
    
    try:
        if choice == "1":
            asyncio.run(test_final_copy_solution())
        elif choice == "2":
            asyncio.run(test_copy_button_vs_dom_parsing())
        else:
            print("❌ 无效选择")
            return
            
        print("\n✅ 测试完成")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
