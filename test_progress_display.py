#!/usr/bin/env python3
"""
测试实时进度显示功能
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.cli import ChatCLI
from src.config import BaiduChatConfig


async def test_progress_display():
    """测试实时进度显示"""
    print("🧪 测试隐蔽模式实时进度显示功能...")

    # 创建CLI实例
    cli = ChatCLI()

    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        keep_browser_open=True,  # 保持浏览器打开
        response_timeout=180000  # 180秒超时
    )

    try:
        # 测试单次消息模式（隐蔽模式）
        print("\n📝 测试隐蔽模式单次消息的进度显示...")
        await cli.single_message_mode("你好，请简单介绍一下你自己", config)

        print("\n✅ 隐蔽模式单次消息测试完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_progress_display())
