#!/usr/bin/env python3
"""
调试AI回复提取 - 查看页面上的实际内容
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def debug_ai_response():
    """调试AI回复提取"""
    print("🔍 调试AI回复提取...")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=180000  # 180秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复完成...")
            
            # 等待回答完成
            max_wait_time = 120
            for wait_time in range(0, max_wait_time, 5):
                completion_status = await bot._check_completion_status()
                if completion_status:
                    print(f"✅ 在第{wait_time}秒检测到回答完成！")
                    break
                print(f"⏳ 等待中... {wait_time}秒")
                await asyncio.sleep(5)
            else:
                print("❌ 未检测到回答完成状态，但继续尝试提取...")
            
            # 等待一下确保内容完全加载
            await asyncio.sleep(3)
            
            print("\n🔍 开始分析页面内容...")
            
            # 获取页面上所有可能的文本内容
            all_texts = await bot.page.evaluate("""
                () => {
                    const allElements = document.querySelectorAll('div, p, span');
                    const texts = [];
                    
                    for (let element of allElements) {
                        const text = element.innerText || '';
                        if (text.length > 10 && text.length < 1000) {
                            texts.push({
                                text: text.trim(),
                                length: text.length,
                                tagName: element.tagName,
                                className: element.className || '',
                                id: element.id || ''
                            });
                        }
                    }
                    
                    // 按长度排序
                    texts.sort((a, b) => b.length - a.length);
                    return texts.slice(0, 10); // 返回前10个最长的文本
                }
            """)
            
            print(f"\n📊 找到 {len(all_texts)} 个可能的文本内容:")
            for i, item in enumerate(all_texts, 1):
                print(f"\n[{i}] 长度: {item['length']}")
                print(f"    标签: {item['tagName']}")
                print(f"    类名: {item['className'][:50]}...")
                print(f"    ID: {item['id']}")
                print(f"    内容: {item['text'][:100]}...")
                
                # 检查是否可能是AI回复
                if (item['length'] > 20 and 
                    'DeepSeek-R1满血版' not in item['text'] and
                    '思考中' not in item['text'] and
                    '登录' not in item['text'] and
                    '注册' not in item['text']):
                    print(f"    🎯 可能是AI回复!")
            
            # 尝试使用新的提取方法
            print(f"\n🧪 尝试使用新的提取方法...")
            try:
                response = await bot._extract_ai_response()
                print(f"✅ 成功提取AI回复:")
                print(f"📥 {response}")
            except Exception as e:
                print(f"❌ 提取失败: {e}")
            
            # 保持浏览器打开以便手动检查
            print(f"\n⏸️  浏览器将保持打开30秒，请手动检查页面内容...")
            await asyncio.sleep(30)
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(debug_ai_response())
