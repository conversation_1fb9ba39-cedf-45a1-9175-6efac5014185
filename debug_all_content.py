#!/usr/bin/env python3
"""
调试所有页面内容 - 找出AI回复在哪里
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def debug_all_content():
    """调试所有页面内容"""
    print("🔍 调试所有页面内容")
    print("=" * 50)
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=120000  # 120秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 发送消息
            test_message = "你好"
            print(f"📤 发送消息: {test_message}")
            
            # 手动执行发送过程
            await bot._input_message_stealth(test_message)
            await bot._click_send_button_stealth()
            
            print("✅ 消息已发送，等待回复完成...")
            
            # 等待回答完成
            max_wait_time = 60
            for wait_time in range(0, max_wait_time, 2):
                completion_status = await bot._check_completion_status()
                if completion_status:
                    print(f"✅ 在第{wait_time}秒检测到回答完成！")
                    break
                await asyncio.sleep(2)
            else:
                print("❌ 未检测到回答完成状态")
                return
            
            # 等待一下确保内容完全加载
            await asyncio.sleep(3)
            
            print("\n🔍 开始分析所有页面内容...")
            
            # 获取所有包含"你好"的元素
            all_elements = await bot.page.evaluate("""
                () => {
                    const allDivs = document.querySelectorAll('div');
                    const results = [];
                    
                    for (let div of allDivs) {
                        const text = div.innerText || '';
                        
                        // 查找包含"你好"的所有元素
                        if (text.includes('你好')) {
                            results.push({
                                text: text.trim(),
                                length: text.length,
                                className: div.className,
                                id: div.id,
                                tagName: div.tagName
                            });
                        }
                    }
                    
                    return results;
                }
            """)
            
            print(f"\n📊 找到 {len(all_elements)} 个包含'你好'的元素:")
            for i, elem in enumerate(all_elements, 1):
                print(f"\n[{i}] 元素信息:")
                print(f"    标签: {elem['tagName']}")
                print(f"    类名: {elem['className']}")
                print(f"    ID: {elem['id']}")
                print(f"    长度: {elem['length']}")
                print(f"    内容: {elem['text'][:200]}...")
                
                # 检查是否包含完整的AI回复
                if ('你好呀' in elem['text'] and 
                    '很高兴见到你' in elem['text'] and 
                    '😊' in elem['text']):
                    print(f"    🎉 这个元素包含完整的AI回复！")
                    print(f"    完整内容: {elem['text']}")
                    return elem['text']
            
            # 如果没找到，尝试查找包含表情符号的元素
            print(f"\n🔍 查找包含表情符号的元素...")
            emoji_elements = await bot.page.evaluate("""
                () => {
                    const allDivs = document.querySelectorAll('div');
                    const results = [];
                    
                    for (let div of allDivs) {
                        const text = div.innerText || '';
                        
                        // 查找包含表情符号的元素
                        if (text.includes('👋') || text.includes('😊')) {
                            results.push({
                                text: text.trim(),
                                length: text.length,
                                className: div.className,
                                id: div.id
                            });
                        }
                    }
                    
                    return results;
                }
            """)
            
            print(f"\n📊 找到 {len(emoji_elements)} 个包含表情符号的元素:")
            for i, elem in enumerate(emoji_elements, 1):
                print(f"\n[{i}] 表情元素:")
                print(f"    类名: {elem['className']}")
                print(f"    ID: {elem['id']}")
                print(f"    长度: {elem['length']}")
                print(f"    内容: {elem['text']}")
                
                # 检查是否是AI回复
                if (elem['length'] > 20 and elem['length'] < 500 and
                    '你好呀' in elem['text']):
                    print(f"    🎉 找到AI回复！")
                    return elem['text']
            
            print("\n❌ 未找到AI回复内容")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    try:
        result = asyncio.run(debug_all_content())
        if result:
            print(f"\n🎉 成功找到AI回复:")
            print(f"📥 {result}")
        else:
            print(f"\n❌ 未找到AI回复")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断调试")
    except Exception as e:
        print(f"\n❌ 调试程序异常: {e}")


if __name__ == "__main__":
    main()
