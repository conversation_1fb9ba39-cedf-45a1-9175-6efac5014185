#!/usr/bin/env python3
"""
测试修复后的AI回复识别功能
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.stealth_bot import StealthBaiduChatBot
from src.config import BaiduChatConfig


async def test_response_validation():
    """测试回复验证功能"""
    print("🧪 测试AI回复验证功能...")
    
    # 创建配置
    config = BaiduChatConfig(
        headless=False,  # 显示浏览器窗口
        debug=True,      # 启用调试
        response_timeout=30000  # 30秒超时
    )
    
    try:
        async with StealthBaiduChatBot(config) as bot:
            print("✅ 浏览器初始化成功")
            
            # 测试验证函数
            test_cases = [
                # 应该被识别为有效AI回复的文本
                ("你好呀！👋我是DeepSeek-R1，由中国的「深度求索」团队研发的一款智能AI助手。", True),
                ("我可以帮助你解答各种问题，完成各种任务～✨", True),
                ("根据你的问题，我来为你详细解答一下...", True),
                
                # 应该被识别为无效的文本（推荐问题等）
                ("你如何获取知识的？\n你可以帮我写论文吗？\n你是怎么被研发出来的？", False),
                ("提问", False),
                ("发送", False),
                ("搜索", False),
                ("开启新对话", False),
                ("深度搜索", False),
                ("快速回答", False),
                ("问问智能体", False),
            ]
            
            print("\n🔍 测试回复验证逻辑:")
            all_passed = True
            
            for text, expected in test_cases:
                result = bot._is_valid_ai_response(text)
                status = "✅" if result == expected else "❌"
                print(f"{status} 文本: {text[:50]}...")
                print(f"    期望: {expected}, 实际: {result}")
                
                if result != expected:
                    all_passed = False
            
            if all_passed:
                print("\n🎉 所有验证测试通过！")
            else:
                print("\n❌ 部分验证测试失败")
            
            # 测试实际发送消息
            print("\n📤 测试实际消息发送...")
            test_message = "你好，请简单介绍一下你自己"
            print(f"发送消息: {test_message}")
            
            try:
                response = await bot.send_message_stealth(test_message)
                print(f"✅ 收到回复: {response[:100]}...")
                
                # 验证回复是否有效
                if bot._is_valid_ai_response(response):
                    print("✅ 回复验证通过 - 这是有效的AI回复")
                else:
                    print("❌ 回复验证失败 - 这可能不是真正的AI回复")
                    
            except Exception as e:
                print(f"❌ 发送消息失败: {e}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🔧 AI回复识别修复测试")
    print("=" * 50)
    
    try:
        asyncio.run(test_response_validation())
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    main()
